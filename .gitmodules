[submodule "app/assets"]
	path = app/assets
	url = **********************:flutter/resources/gp_assets.git
	branch = develop
[submodule "app/features/calendar"]
	path = app/features/calendar
	url = **********************:flutter/features/calendar/gp_feat_calendar.git
[submodule "app/features/member_picker"]
	path = app/features/member_picker
	url = **********************:flutter/features/gp_feat_member_picker.git
[submodule "resources/docs"]
	path = resources/docs
	url = **********************:flutter/resources/gp_docs.git
	branch = develop
[submodule "app/features/time_keeping"]
	path = app/features/time_keeping
	url = **********************:flutter/features/time_keeping/gp_feat_time_keeping.git
[submodule "app/shared/gp_shared"]
	path = app/shared/gp_shared
	url = **********************:flutter/components/gp_shared.git
[submodule "app/features/drive"]
	path = app/features/drive
	url = **********************:flutter/features/gp_drive.git
[submodule "app/features/native_shared"]
	path = app/features/native_shared
	url = **********************:flutter/features/gp_native_shared.git
[submodule "app/features/ticket"]
	path = app/features/ticket
	url = **********************:flutter/features/gp_ticket.git
[submodule "app/features/ca"]
	path = app/features/ca
	url = **********************:flutter/features/gp_ca.git
[submodule "app/core/gp_core_v2"]
	path = app/core/gp_core_v2
	url = **********************:flutter/core/gp_core_v2.git
[submodule "app/core/gp_core"]
	path = app/core/gp_core
	url = **********************:flutter/core/gp_core.git
[submodule "app/shared/gp_shared_dep"]
	path = app/shared/gp_shared_dep
	url = **********************:flutter/components/gp_shared_dep.git
[submodule "app/features/task"]
	path = app/features/task
	url = **********************:flutter/features/task/gp_feat_task.git
[submodule "app/features/coin"]
	path = app/features/coin
	url = **********************:flutter/features/gp_coin.git
[submodule "app/features/portal"]
	path = app/features/portal
	url = **********************:flutter/features/gp_portal.git
[submodule "app/app/features/drive"]
	path = app/app/features/drive
	url = **********************:flutter/features/gp_drive.git
[submodule "app/features/project_management"]
	path = app/features/project_management
	url = **********************:flutter/features/project_management.git
