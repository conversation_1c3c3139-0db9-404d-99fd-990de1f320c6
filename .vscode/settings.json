{
    "editor.inlineSuggest.showToolbar": "onHover",
    "folder-path-color.folders": [
        // {
        //     "path": "app/lib",
        //     "symbol": "Ap",
        //     "tooltip": "App",
        // },
        // {
        //     "path": "app/core",
        //     "symbol": "Co",
        //     "tooltip": "Core",
        // },
        // {
        //     "path": "app/features",
        //     "symbol": "Fe",
        //     "tooltip": "Features"
        // },
        {
            "path": "app/features/ticket/lib/domain",
            "symbol": "DO",
            "tooltip": "Drive Domain"
        },
        {
            "path": "app/features/ticket/lib/data",
            "symbol": "DA",
            "tooltip": "Drive Data"
        },
        {
            "path": "app/shared/gp_shared/lib/domain",
            "symbol": "DO",
            "tooltip": "Domain"
        },
        {
            "path": "app/shared/gp_shared/lib/data",
            "symbol": "DA",
            "tooltip": "Data"
        }
    ],
    "psi-header.config": {
        "forceToTop": true,
        "blankLinesAfter": 1,
        "spacesBetweenYears": false,
        "license": "GAPO 2023",
        // "author": "ToanNM",
        // "initials": "ToanNM",
        // "authorEmail": "<EMAIL>",
        "company": "GAPO",
        "copyrightHolder": "GAPO",
        "creationDateZero": "asIs",
        "hostname": "gapowork.vn"
    },
    "psi-header.changes-tracking": {
        "isActive": true,
        "modAuthor": "Modified By:",
        "modDate": "Last Modified:",
        "modDateFormat": "date",
        "include": [],
        "includeGlob": [
            "**/data/*",
            "gp_shared/*",
            "/gp_shared/*",
            "**/*.service.dart",
            "**/*.service_v2.dart",
            "**/*.dto.dart",
            // "**/*.config.dart",
            "**/*.configs.dart",
            "**/*.module.dart",
            "**/*.constants.dart",
            "**/*.component.dart",
            "**/*.components.dart",
            "**/*.mapper.dart",
            "**/mapper.dart",
            "**/*.interceptor.dart",
            "**/*.entity.dart",
            "**/entity/*",
            "**/main.dart",
            "**/model/*",
            "**/model/**/*.dart",
            "**/models/*",
            "**/usecase/*",
            "**/*.usecase.dart",
            "**.usecase.dart",
            "**/repository/*.repo.dart",
            "**/repository/*_repo.dart",
            "**/*.repo.dart",
            "**/*_repo.dart",
            "**/mapper/*_mapper.dart",
            "**/*_mapper.dart",
            "**/tus/*_.dart",
            "**/tus_*.dart",
            "**/*.route.dart",
            "**/*.popup.dart",
            "**/*_codec.dart",
            "**/*_behavior.dart",
            "**/*.behavior.dart",
        ],
        "exclude": [
            "markdown",
            "json",
            "jsonc",
            "shellscript"
        ],
        "excludeGlob": [],
        "autoHeader": "autoSave",
        "enforceHeader": true,
        "replace": [
            "Filename:",
            "Project",
            "Copyright"
        ],
        "updateLicenseVariables": false
    },
    "psi-header.variables": [
        [
            "manager",
            "Gapo Flutter Team"
        ],
        [
            "projectCreationYear",
            "2021"
        ]
    ],
    // "Created Date: <<filecreated(dd/MM/yyyy HH:mm:ss)>>",
    "psi-header.templates": [
        {
            "language": "*",
            "template": [
                "Created Date: <<filecreated('dddd, Do MMMM YYYY, HH:mm:ss')>>",
                "Author: <<author>>",
                "-----",
                "Last Modified: <<dateformat('dddd, Do MMMM YYYY HH:mm:ss')>>",
                "Modified By: <<author>>",
                "-----",
                "Copyright (c) <<projectCreationYear>> - <<year>> <<company>>",
                // "-----",
                // "HISTORY:",
                // "Date      \tBy\tComments",
                // "----------\t---\t---------------------------------------------------------"
            ],
            "changeLogCaption": "HISTORY",
            "changeLogHeaderLineCount": 2,
            "changeLogEntryTemplate": [
                "",
                "<<author>>\t<date>\t"
            ],
            "changeLogNaturalOrder": false,
            "changeLogFooterLineCount": 0
        },
        {
            "language": "dart",
            "mapTo": "dart"
        }
    ],
    // "psi-header.lang-config": [
    //     {
    //         "language": "*",
    //         "begin": "/*",
    //         "prefix": " * ",
    //         "suffix": " *",
    //         "lineLength": 80,
    //         "end": " */",
    //         "forceToTop": false,
    //         "blankLinesAfter": 1,
    //         "beforeHeader": [],
    //         "afterHeader": [],
    //         "rootDirFileName": "gapo_flutter",
    //         "modAuthor": "Modified By:",
    //         "modDate": "Last Modified:",
    //         "modDateFormat": "dd/MM/yyyy hh:mm:ss a",
    //         "ignoreLines": []
    //     },
    //     {
    //         "language": "dart",
    //         "mapTo": "dart"
    //     }
    // ],
    "files.exclude": {
        "**/.git": true,
        "**/.DS_Store": true,
        "**/node_modules": true,
        "node_modules": true,
        // brick templates
        "**/bricks/**": true,
        "mason.yaml": true,
        "brick.yaml": true,
        "**/.mason/**": true,
        "mason-lock.json": true,
        "**/FG/**": true,
    },
    "search.exclude": {
        "/bricks/**": true,
        "**/bricks/**": true,
        ".VSCodeCounter": true,
        ".android": true,
    },
    "files.watcherExclude": {
        "/bricks/**": true,
        "**/bricks/**": true,
        ".VSCodeCounter": true,
        ".android": true,
    },
    "problems.decorations.enabled": false,
    "dart.analysisExcludedFolders": [
        "bricks",
    ],
    // "dart.flutterSdkPath": "/Users/<USER>/fvm/default/bin"
}
