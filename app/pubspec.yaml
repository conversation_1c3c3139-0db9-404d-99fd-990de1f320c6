_internal_variables_:
  - &gp_feature_ref "develop"
  - &gp_default_ref "develop"
  - &gp_module_ref "feat/3.22.2"
# _internal_variables_:
#   - &gp_time_picker "build/android/time_picker"

name: gapo_flutter_app
description: Gapo Flutter app
publish_to: "none"
version: 5.4.1

environment:
  sdk: ">=3.6.0 <4.0.0"
  flutter: 3.29.1

workspace:
  ### Các modules cần sử dụng tại local
  ### default sẽ sử dụng toàn bộ ở remote
  - assets
  - core/gp_core
  - shared/gp_shared
  - shared/gp_shared_dep
  - features/calendar
  # - features/coin
  # - features/drive
  - features/member_picker
  # - features/native_shared
  - features/task
  - features/time_keeping
  - features/ticket
  - features/portal
  # - features/project_management

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  ########## Gapo Flutter Modules ##########
  gp_assets:
    git:
      url: **********************:flutter/resources/gp_assets.git
      ref: *gp_feature_ref

  gp_core:
    git:
      url: **********************:flutter/core/gp_core.git
      ref: *gp_feature_ref

  gp_shared_dep:
    git:
      url: **********************:flutter/components/gp_shared_dep.git
      ref: *gp_feature_ref

  gp_feat_task:
    git:
      url: **********************:flutter/features/task/gp_feat_task.git
      ref: *gp_feature_ref

  gp_feat_calendar:
    git:
      url: **********************:flutter/features/calendar/gp_feat_calendar.git
      ref: *gp_feature_ref

  gp_feat_member_picker:
    git:
      url: **********************:flutter/features/gp_feat_member_picker.git
      ref: *gp_feature_ref

  gp_feat_time_keeping:
    git:
      url: **********************:flutter/features/time_keeping/gp_feat_time_keeping.git
      ref: *gp_feature_ref

  gp_feat_drive:
    git:
      url: **********************:flutter/features/gp_drive.git
      ref: *gp_default_ref

  gp_native_shared:
    git:
      url: **********************:flutter/features/gp_native_shared.git
      ref: *gp_feature_ref

  gp_feat_ticket:
    git:
      url: **********************:flutter/features/gp_ticket.git
      ref: *gp_feature_ref

  gp_feat_coin:
    git:
      url: **********************:flutter/features/gp_coin.git
      ref: *gp_feature_ref

  gp_feat_portal:
    git:
      url: **********************:flutter/features/gp_portal.git
      ref: *gp_feature_ref

  gp_feat_ca:
    git:
      url: **********************:flutter/features/gp_ca.git
      ref: "develop" # chưa lên 3.29.1

  gp_feat_project_management:
    git:
      url: **********************:flutter/features/project_management.git
      ref: *gp_feature_ref

  ########## End of Gapo Flutter Modules ##########

  device_preview: 1.2.0

dependency_overrides:
  device_info_plus: 10.1.0
  intl: ^0.19.0
  http: ^1.0.0
  web: 1.1.0
  timezone: 0.10.0

  freezed_annotation: ^3.0.0
  json_annotation: 4.9.0
  analyzer: 7.3.0

  get_it: 8.0.3
  talker: 4.7.1
  talker_flutter: 4.7.1
  talker_bloc_logger: 4.7.1
  flutter_bloc: 9.1.0
  injectable: 2.5.0
  logger: 2.5.0

  auto_mappr_annotation: 2.3.0
  dio: 5.8.0+1
  retrofit: 4.4.2
  go_router: 14.8.1

  flutter_fancy_tree_view: 1.6.0
  currency_text_input_formatter: 2.2.9
  math_parser: 1.5.1
  jiffy: 6.4.1
  diffutil_dart: 4.0.1
  decimal: 3.2.1
  dotted_line: 3.2.3

    # WIDGETS
  flutter_sticky_header: 0.7.0
  # pull_to_refresh
  # 2.0.0, to remove widget binding ! on flutter 3
  # https://github.com/peng8350/flutter_pulltorefresh/pull/589
  pull_to_refresh:
    git:
      url: https://github.com/miquelbeltran/flutter_pulltorefresh

  cached_network_image: 3.4.1
  expandable: 5.0.1
  syncfusion_flutter_core: 23.1.40

  # UTILITIES
  ########## over 3 years ##########
  # package_info: ^2.0.2
  snap_scroll_physics: 0.0.1+3
  image: 4.5.4

  # video_player:
  #   git:
  #     url: **********************:flutter/plugins.git
  #     path: packages/video_player/video_player
  #     ref: exo/2.17.1

  video_thumbnail:
    git:
      url: **********************:flutter/video_thumbnail.git
      ref: master

  # gallery_saver:
  #   git:
  #     url: https://github.com/hoangthai9217/gallery_saver
  #     ref: 12ba62d

  ########## end of over 3 years ##########

  video_player: 2.9.5
  package_info_plus: 8.3.0
  gallery_saver_plus: 3.2.4
  # share_plus: 10.1.4

  url_launcher: 6.3.1
  url_launcher_android: 6.3.15
  url_launcher_ios: 6.3.2
  url_launcher_macos: 3.2.2
  url_launcher_linux: 3.2.1
  url_launcher_web: 2.4.0
  url_launcher_windows: 3.1.4

  rxdart: 0.28.0
  tiengviet: 1.0.0
  detectable_text_field: 3.0.2
  shimmer: 3.0.0
  loadmore: 2.1.0

  # File picker
  file_picker: 9.2.2
  image_picker: 1.1.2
  flutter_plugin_android_lifecycle: 2.0.20 #2.0.27
  # better_open_file: ^3.6.3
  open_filex: 4.7.0
  android_id: 0.4.0

  flutter_video_info:
    git:
      url: **********************:flutter/utils/flutter_video_info.git
      ref: *gp_default_ref

  # image_save: ^5.0.0

  # super_clipboard:
  # git:
  #   url: **********************:flutter/utils/gp_super_clipboard.git
  #   ref: "develop"

  connectivity_plus: 6.1.3

  photo_view: 0.15.0

  path_provider:
  path:

  # Networking
  emoji_picker_flutter: 4.3.0
  flutter_keyboard_visibility: 6.0.0

  flutter_widget_from_html_core: 0.16.0

  flutter_secure_storage: 9.2.4

  time: 2.1.5

  stack_trace: 1.12.1

  app_settings: 5.2.0

  flutter_udid: 4.0.0

  sentry: 8.14.0
  sentry_flutter: 8.14.0
  sentry_logging: 8.14.0
  sentry_dio: 8.14.0
  logging: 1.3.0
  # flutter_vlc_player: 7.4.3

  image_gallery_saver_plus: 4.0.1
  webview_flutter: 4.10.0

  flutter_timezone: 4.1.0
  diff_match_patch: 0.4.1
  native_exif: 0.6.0
  geocoding: 3.0.0
  device_safety_info: 0.0.9
  geolocator: 13.0.3
  location: 8.0.0
  doc_widget: 0.4.0

  flutter_calendar_carousel: 2.5.3
  lottie: 3.3.1

  percent_indicator: 4.2.4
  sticky_headers: 0.3.0+2
  mime: 2.0.0
  speed_test_dart: 1.0.5+0
  cross_file: 0.3.4+2

  cupertino_icons: 1.0.8
  equatable: 2.0.7
  get: 4.7.2
  get_storage: 2.1.1

  gp_core_v2:
    git:
      url: **********************:flutter/core/gp_core_v2.git
      ref: "develop"
      path: gp_core_v2

  gp_email_tags:
    git:
      url: **********************:flutter/gp_email_tags.git
      ref: *gp_module_ref

  gp_markdown_widget:
    git:
      url: **********************:flutter/gpmdw.git
      ref: *gp_module_ref

  flutter_quill:
    git:
      url: **********************:flutter/rtfeditor.git
      ref: *gp_module_ref

  gp_dio_log:
    git:
      url: **********************:flutter/gp_dio_log.git
      ref: *gp_module_ref

  msal_flutter:
    git:
      # url: **********************:flutter/msal-flutter.git
      url: **********************:flutter/utils/msal-flutter.git
      ref: *gp_default_ref

  gp_analysis:
    git:
      url: **********************:flutter/core/gp_analysis.git
      ref: *gp_default_ref

  gp_calendar:
    git:
      url: **********************:flutter/gpcalendar.git
      ref: *gp_default_ref

  source_gen:
    git:
      url: **********************:flutter/utils/gp_source_gen.git
      path: "source_gen"
      ref: "feat/3.29.1"

  # dev:
  flutter_lints: 5.0.0

  build_runner: 2.4.15
  injectable_generator: 2.7.0
  retrofit_generator: 9.1.9
  auto_mappr: 2.8.0
  freezed: 3.0.4
  go_router_builder: 2.8.2
  json_serializable: 6.9.4
  # mock_web_server: ^5.0.0-nullsafety.1
  # pubspec_dependency_sorter: 1.0.5
  doc_widget_builder: 0.4.0
  dart_style: 3.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  melos: 7.0.0-dev.8

flutter:
  uses-material-design: true

  module:
    androidX: true
    androidPackage: vn.gapowork.android
    iosBundleIdentifier: com.example.gapoFlutter

  # gp_feat_album:
  #   git:
  #     url: **********************:flutter/features/gp_feat_album.git
  #     ref: *gp_feature_ref

melos:
  name: modular

  packages:
    # - app/*
    - app/resources/**
    - app/assets/*
    - app/core/**
    - app/features/**
    - app/shared/**

  ignore:
    - 'packages/**/example'
    - 'app/**/example'
    
  scripts:
    formatting:check: melos exec -- dart format --set-exit-if-changed . #flutter format

    analyze: melos exec -- dart analyze --fatal-infos

    test-coverage:
      run: melos exec --dir-exists=test --flutter -- "flutter test --coverage ; genhtml ./coverage/lcov.info --output-directory ./coverage/out"
      description: Run flutter test with coverage and generates coverage report

    generate:
      run: melos exec $MELOS_FILTERS -c 1 --depends-on="build_runner" -- "dart run build_runner build --delete-conflicting-outputs"
      description: Build all generated files.

    pub-sort:
      run: melos exec $MELOS_FILTERS -c 1 --depends-on="build_runner" -- "dart run pubspec_dependency_sorter"
      description: Sort all pubspec dependencies.

    check-format:
      exec: dart format --set-exit-if-changed .
      description: Check the format of a specific package in this project.

    format:
      exec: dart format .
      description: Format a specific package in this project.

    version:
      description: Updates version numbers in all build files
      run: bash scripts/version.sh

    test:
      run: melos run test:dart --no-select ; melos run test:flutter --no-select
      description: Run all Dart & Flutter tests in this project.

    test:dart:
      run: melos exec -c 1 --fail-fast -- "dart test test"
      description: Run Dart tests for a specific package in this project.
      # select-package:
      #   flutter: false
      #   dir-exists: test

      packageFilters:
        noPrivate: true
        dirExists: test

    test:flutter:
      run: melos exec -c 1 --fail-fast -- "flutter test test"
      description: Run Flutter tests for a specific package in this project.
      # select-package:
      #   flutter: true
      #   dir-exists: test

      packageFilters:
        noPrivate: true
        dirExists: test

    pana:
      run: melos exec -c 10 -- "../../tool/verify_pub_score.sh"
      # select-package:
      #   no-private: true
      packageFilters:
        noPrivate: true
      description: Checks if the package meets the pub.dev score requirement.

    pu:
      packageFilters:
        flutter: true
        
      run: bash "script/git/remove_pubspec_overrides.sh" ; melos exec -c 1 flutter pub upgrade --major-versions
      
      description: Upgrade all dependencies.

    pg:
      packageFilters:
        flutter: true
        
      run: bash "script/git/remove_pubspec_overrides.sh"; bash "script/flutter/clean.sh"
      
      description: Clean and Pub get all dependencies (apply for ios)

    fpg:
      packageFilters:
        flutter: true
        
      run: bash "script/git/remove_pubspec_overrides.sh" ;  melos exec -c 1 flutter pub get
      
      description: Pub get all dependencies

    bump:
      packageFilters:
        flutter: true
        
      run: melos exec -c 1 git add . ; melos exec -c 1 git commit -am 'chore:\ Bump'
      
      description: Add and commit all.

  command:
    version:
      releaseUrl: true
      updateGitTagRefs: true
      workspaceChangelog: true
      message: |
        chore: cut package releases

        {new_package_versions}
    
    # postbootstrap: melos bs

    bootstrap:
      usePubspecOverrides: true

  # sdkPath: app/.fvm/flutter_sdk
