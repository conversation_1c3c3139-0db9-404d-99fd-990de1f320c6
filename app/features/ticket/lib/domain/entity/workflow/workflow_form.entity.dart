/*
 * Created Date: Wednesday, 24th July 2024, 09:17:31
 * Author: gapo
 * -----
 * Last Modified: Friday, 11th April 2025 17:53:28
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:gp_core/core.dart';

import '../../../../domain/entity/enums/ticket/ticket_form_field_type.dart';

part 'workflow_form.entity.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkFlowFieldValuesEntity {
  WorkFlowFieldValuesEntity({
    required this.workspaceId,
    required this.fieldId,
    required this.ticketId,
    this.unitId,
    required this.info,
    required this.value,
    this.isExpanded,
    this.parentId,
  });

  final String workspaceId;

  final int fieldId;
  int? parentId;

  final int ticketId;

  final dynamic unitId;

  dynamic value;

  final WorkFlowFormFieldEntity info;

  bool? isExpanded;

  Map<String, dynamic> toValueJson() {
    return {
      'id': fieldId,
      'type': info.type.type,
      'value': value,
      'parent_id': info.parentId,
    };
  }

  factory WorkFlowFieldValuesEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkFlowFieldValuesEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkFlowFieldValuesEntityToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is WorkFlowFieldValuesEntity &&
        other.workspaceId == workspaceId &&
        other.fieldId == fieldId &&
        other.ticketId == ticketId &&
        other.unitId == unitId &&
        other.value == value &&
        other.info == info &&
        other.isExpanded == isExpanded &&
        other.parentId == parentId;
  }

  @override
  int get hashCode {
    return Object.hash(
      workspaceId,
      fieldId,
      ticketId,
      unitId,
      value,
      info,
      isExpanded,
      parentId,
    );
  }
}

class WorkFlowFormEntity {
  WorkFlowFormEntity({
    required this.workspaceId,
    required this.workflowId,
    required this.fields,
  });

  final String workspaceId;

  final int workflowId;

  final List<WorkFlowFormFieldEntity> fields;
}

@JsonSerializable(explicitToJson: true)
class WorkFlowFormFieldEntity {
  WorkFlowFormFieldEntity({
    required this.workflowId,
    required this.id,
    required this.type,
    required this.title,
    required this.hint,
    required this.isRequired,
    required this.option,
    this.workspaceId,
    this.version,
    this.parentId,
    this.clientId,
    this.createdAt,
    this.updatedAt,
    this.readOnly = false,
    this.isExpanded,
  });

  final int workflowId;
  final dynamic workspaceId;

  final int id;

  final int? parentId, clientId;

  final int? version;

  final DateTime? createdAt, updatedAt;

  final bool isRequired;

  final String title;
  final String? hint;

  final TicketFormFieldType type;

  final WorkFlowFormOptionEntity option;

  bool readOnly;

  String currentText = '';

  bool? isExpanded;

  factory WorkFlowFormFieldEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkFlowFormFieldEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkFlowFormFieldEntityToJson(this);
  // Map<int, bool> get selectedIndexs {
  //   final hasChildren =
  //       option.selections != null && option.selections!.isNotEmpty;

  //   if (hasChildren) {
  //     for (var element in option.selections!) {
  //       if (element.isSelected) {
  //         return {element.fields?.first.option.columns?.length ?? 0: true};
  //       }
  //     }
  //   }

  //   return {};
  // }

  WorkFlowFormFieldEntity copyWith({
    int? workflowId,
    dynamic workspaceId,
    int? id,
    int? parentId,
    clientId,
    version,
    DateTime? createdAt,
    updatedAt,
    bool? isRequired,
    String? title,
    hint,
    TicketFormFieldType? type,
    WorkFlowFormOptionEntity? option,
    bool? isExpanded,
  }) {
    return WorkFlowFormFieldEntity(
      workflowId: workflowId ?? this.workflowId,
      workspaceId: workspaceId ?? this.workspaceId,
      id: id ?? this.id,
      parentId: parentId ?? this.parentId,
      clientId: clientId ?? this.clientId,
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isRequired: isRequired ?? this.isRequired,
      title: title ?? this.title,
      hint: hint ?? this.hint,
      type: type ?? this.type,
      option: option ?? this.option,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  WorkFlowFieldValuesEntity toFieldValue(
      WorkFlowFieldValuesEntity? fieldValue) {
    return WorkFlowFieldValuesEntity(
      workspaceId: workspaceId,
      fieldId: id,
      ticketId: fieldValue?.ticketId ?? 0,
      unitId: fieldValue?.unitId,
      parentId: fieldValue?.fieldId,
      info: WorkFlowFormFieldEntity(
        workflowId: workflowId,
        id: id,
        type: type,
        title: title,
        hint: hint,
        isRequired: isRequired,
        option: option,
      ),
      value: fieldValue?.value,
    );
  }

  WorkFlowFieldValuesEntity toEmptyFieldValue() {
    return WorkFlowFieldValuesEntity(
      workspaceId: workspaceId,
      fieldId: id,
      ticketId: 0,
      unitId: 0,
      info: WorkFlowFormFieldEntity(
        workflowId: workflowId,
        id: id,
        type: type,
        title: title,
        hint: hint,
        isRequired: isRequired,
        option: option,
      ),
      value: null,
    );
  }
}

@JsonSerializable(explicitToJson: true)
class WorkFlowFormOptionEntity {
  WorkFlowFormOptionEntity({
    required this.isThousandSeparator,
    required this.keepDecimalPlaces,
    required this.secondTitle,
    required this.thirdTitle,
    required this.unit,
    required this.timeFormat,
    required this.formula,
    required this.content,
    this.index,
    this.minValue,
    this.maxValue,
    this.formulaArgs,
    this.currencyPool,
    this.selectionPool,
    this.selections,
    this.columns,
  });

  final bool isThousandSeparator;

  final int keepDecimalPlaces;

  final int? index;

  final double? minValue, maxValue;

  final String secondTitle, thirdTitle, unit, timeFormat, formula, content;

  final List<int>? formulaArgs;

  final List<int>? currencyPool;
  final List<String>? selectionPool;
  final List<WorkFlowFormSelectionEntity>? selections;
  final List<WorkFlowFormColumnEntity>? columns;

  factory WorkFlowFormOptionEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkFlowFormOptionEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkFlowFormOptionEntityToJson(this);

  WorkFlowFormOptionEntity copyWith({
    bool? isThousandSeparator,
    int? keepDecimalPlaces,
    int? index,
    double? minValue,
    maxValue,
    String? secondTitle,
    thirdTitle,
    unit,
    timeFormat,
    formula,
    content,
    List<int>? formulaArgs,
    List<int>? currencyPool,
    List<String>? selectionPool,
    List<WorkFlowFormSelectionEntity>? selections,
    List<WorkFlowFormColumnEntity>? columns,
  }) {
    return WorkFlowFormOptionEntity(
      isThousandSeparator: isThousandSeparator ?? this.isThousandSeparator,
      keepDecimalPlaces: keepDecimalPlaces ?? this.keepDecimalPlaces,
      index: index ?? this.index,
      minValue: minValue ?? this.minValue,
      maxValue: maxValue ?? this.maxValue,
      secondTitle: secondTitle ?? this.secondTitle,
      thirdTitle: thirdTitle ?? this.thirdTitle,
      unit: unit ?? this.unit,
      timeFormat: timeFormat ?? this.timeFormat,
      formula: formula ?? this.formula,
      content: content ?? this.content,
      formulaArgs: formulaArgs ?? this.formulaArgs,
      currencyPool: currencyPool ?? this.currencyPool,
      selectionPool: selectionPool ?? this.selectionPool,
      selections: selections ?? this.selections,
      columns: columns ?? this.columns,
    );
  }

  void deSelected() {
    selections?.forEach((element) {
      element.deSelected();
    });
  }
}

@JsonSerializable(explicitToJson: true)
class WorkFlowFormSelectionEntity {
  WorkFlowFormSelectionEntity({
    required this.value,
    this.isSelected = false,
    this.fields,
  });

  final String value;
  final List<WorkFlowFormFieldEntity>? fields;
  bool isSelected;

  void deSelected() {
    isSelected = false;
  }

  factory WorkFlowFormSelectionEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkFlowFormSelectionEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkFlowFormSelectionEntityToJson(this);
}

@JsonSerializable(explicitToJson: true)
class WorkFlowFormColumnEntity {
  const WorkFlowFormColumnEntity({
    required this.field,
  });

  final WorkFlowFormFieldEntity field;

  factory WorkFlowFormColumnEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkFlowFormColumnEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkFlowFormColumnEntityToJson(this);
}
