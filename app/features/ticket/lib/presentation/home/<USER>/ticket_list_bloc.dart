/*
 * Created Date: Saturday, 20th July 2024, 15:48:24
 * Author: gapo
 * -----
 * Last Modified: Saturday, 20th July 2024 15:48:30
 * Modified By: gapo
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:gp_shared/presentation/base/bloc/bloc.dart';

import '../../../../../mapper/ticket_mapper.dart';
import '../../../../data/model/response/ticket/ticket_list_response.dart';
import '../../../../domain/entity/ticket/ticket_list.entity.dart';
import '../../../data/model/request/ticket/list/ticket_list_params.dart';
import '../../../domain/usecase/ticket/ticket_list.usecase.dart';
import 'ticket_list_state.dart';

final class TicketListBloc extends GPBaseListBlocV2<TicketEntity,
        ListAPIResponseV2<TicketListResponse>, TicketListParams>
    with
        _VariableMixin,
        GPTicketMapperMixin
    implements
        BaseListBehavior<TicketEntity, ListAPIResponseV2<TicketListResponse>,
            TicketListParams> {
  TicketListBloc() {
    setOnUseCaseBehavior(this);
  }

  TicketListLoadedState? lastLoadedState;
  final int currentPage = 1;

  @override
  Future<ListAPIResponseV2<TicketListResponse>> usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) {
    inputData ??= defaultInputData;
    final bool isWorkFlowParams = inputData is TicketListParams;
    assert(isWorkFlowParams);

    if (isWorkFlowParams && page != null) {
      inputData = inputData.copyWith(page: page);
    }

    return ticketListUseCase.execute(TicketListInput(params: inputData));
  }

  @override
  Future<ListAPIResponseV2<TicketListResponse>?> usecaseOnSearchData({
    required TicketListParams params,
    dynamic inputData,
    bool isRefreshData = false,
    String? nextLink,
    int? page,
  }) {
    final bool isTicketListParams = inputData is TicketListParams;
    assert(isTicketListParams);

    if (isTicketListParams) {
      TicketListParams input = inputData;
      if (isTicketListParams && page != null) {
        input = input.copyWith(page: page);
      }
      return ticketListUseCase.execute(TicketListInput(params: input));
    }

    return Future.value(ListAPIResponseV2<TicketListResponse>(
      data: null,
    ));
  }

  @override
  Future<TicketListLoadedState> emitData({
    ListAPIResponseV2<TicketListResponse>? response,
    List<TicketEntity>? entityData,
    required Emitter<BaseListState> emit,
    required bool isInitialLoad,
  }) async {
    final List<TicketEntity> wrapperEntities = [];
    bool canNextPage = false;

    if (response != null && response.data?.isNotEmpty == true) {
      canNextPage = true;
      wrapperEntities.addAll(
        convertList<TicketListResponse, TicketEntity>(response.data!),
      );
    }

    lastLoadedState = TicketListLoadedState(
      isInitialLoad: isInitialLoad,
      data: wrapperEntities,
      canNextPage: canNextPage,
      page: isInitialLoad ? 1 : currentPage,
      nextLink: nextLink(response?.links),
    );

    return lastLoadedState!;
  }
}

mixin _VariableMixin {
  final TicketListUseCase ticketListUseCase = GetIt.I<TicketListUseCase>();
}
