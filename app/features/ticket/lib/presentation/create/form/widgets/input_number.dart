import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_form_field_type.dart';
import 'package:gp_feat_ticket/presentation/create/form/widgets/base/base_input_text_params.dart';
import 'package:gp_feat_ticket/route/popup/popup.dart';

import '../../../../../widgets/base/base_ticket_input_behavior.dart';
import '../../ticket_create.page.dart';
import '../../widgets/shared/title_widget.dart';
import 'base_form_input.dart';
import 'number/number.dart';

// ignore: must_be_immutable
final class TicketCreateInputNumberWidget<
        P extends TicketCreateInputNumberParams>
    extends TicketCreateInputBaseInputWrapperWidget<P> {
  TicketCreateInputNumberWidget({
    required super.inputBehavior,
    required super.params,
    this.onParamsUpdated,
    super.readyOnlyWidget,
    super.key,
    this.isCreatingTicket = false,
  }) {
    inputWidget = _TicketCreateInputNumberWidget<P>(
      inputBehavior: inputBehavior,
      params: params,
      onParamsUpdated: onParamsUpdated,
      isCreatingTicket: isCreatingTicket,
    );

    readyOnlyWidget = _TicketCreateInputNumberReadOnlyWidget(
      params: params,
    );
  }

  final OnParamsUpdated? onParamsUpdated;
  final bool isCreatingTicket;
}

class _TicketCreateInputNumberWidget<P extends TicketCreateInputNumberParams>
    extends TicketCreateInputBaseInputWidget<P> {
  const _TicketCreateInputNumberWidget({
    required super.inputBehavior,
    required super.params,
    this.editingController,
    this.onParamsUpdated,
    required super.isCreatingTicket,
    super.key,
  });

  final TextEditingController? editingController;

  final OnParamsUpdated? onParamsUpdated;

  @override
  State<_TicketCreateInputNumberWidget<P>> createState() =>
      _TicketCreateInputNumberWidgetState<P>();
}

class _TicketCreateInputNumberWidgetState<
        P extends TicketCreateInputNumberParams>
    extends TicketCreateInputBaseInputState<P,
        _TicketCreateInputNumberWidget<P>> implements BaseTicketInputBehavior {
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController editingController = widget
          .editingController ??
      TextEditingController(
        text: params
            .parseCurrentValueStr(params.workFlowFormFieldEntity.currentText)
            .toString(),
      );

  final ValueNotifier<bool> rxHasText = ValueNotifier(false);
  final ValueNotifier<InputCurrencyEntity?> rxCurrencyEntity =
      ValueNotifier(null);
  final FocusNode inputNumberFocusNode = FocusNode();

  late final keyboardParams = GPNumbericKeyboardParams(
    btnDoneStr: widget.params.btnDoneStr ??
        LocaleKeys.ticket_create_input_number_btn_done.tr,
    decimalDigits: widget.params.keepDecimalPlaces,
    isThousandSeparator: widget.params.isThousandSeparator,
    minValue: widget.params.minValue,
    maxValue: widget.params.maxValue,
    onDone: (textValue) {
      // inputNumberFocusNode.unfocus();

      // FocusManager.instance.primaryFocus?.unfocus();
      // Navigator.pop(context);
      hideKeyboard();
    },
    // onMinValueVerify: (newValue, oldValue) {
    //   rxError.value = validator(newValue);
    // },
    // onMaxValueVerify: (newValue, oldValue) {
    //   rxError.value = validator(newValue);
    // },
  );

  void _onClearText() {
    editingController.text = '';
  }

  @override
  void didUpdateWidget(covariant _TicketCreateInputNumberWidget<P> oldWidget) {
    super.didUpdateWidget(oldWidget);

    _rxKeyboardVisible.value = false;
    params.rxHasFocus.value = false;

    inputNumberFocusNode.unfocus();

    if (oldWidget.params != widget.params) {
      params = widget.params;
      inputNumberFocusNode.removeListener(onTextFieldFocus);
      editingController.removeListener(onTextChanged);
      if (params.isRequired == false) {
        _formKey.currentState?.reset();
      }

      /// chờ 1 lúc, tránh setState bị gọi lại trong khi `didUpdateWidget`
      Future.delayed(const Duration(milliseconds: 100)).then((value) {
        _onClearText();

        _onInit();

        onTextChanged();
      });
    }

    // unfocus khi chuyển màn hình
    // FocusManager.instance.primaryFocus?.unfocus();
    // inputNumberFocusNode.unfocus();
  }

  @override
  void initState() {
    super.initState();

    _onInit();

    onTextChanged();
    hasInitData = params.workFlowFormFieldEntity.currentText.isNotEmpty;
  }

  void _onInit() {
    // params?.workFlowFormFieldEntity.currentText = editingController.text;
    editingController.text =
        widget.params.workFlowFormFieldEntity.currentText.isEmpty
            ? ''
            : widget.params
                .parseCurrentValueStr(
                    widget.params.workFlowFormFieldEntity.currentText)
                .toString();
    if (params.enabled == true) {
      if (params.currencies?.isNotEmpty == true) {
        rxCurrencyEntity.value = params.currencies?.firstWhereOrNull(
              (e) => e.id == params.currentCurrency?.id,
            ) ??
            params.currencies?.first;
      }

      inputNumberFocusNode.addListener(() {
        onTextFieldFocus();
      });

      editingController.addListener(onTextChanged);
    }

    onFormulaInit();
  }

  void onFormulaInit() {
    if (params is TicketCreateInputFormulaParams &&
        params.inputNumberType ==
            TicketCreateInputNumberType.inputTypeFormula) {
      (params as TicketCreateInputFormulaParams).setOnFormulaListener((text) {
        editingController.text = text;

        params.workFlowFormFieldEntity.currentText = editingController.text;
        widget.onParamsUpdated?.call(params);
      });
    }
  }

  void onTextChanged() {
    rxHasText.value = editingController.text.isNotEmpty;

    params.workFlowFormFieldEntity.currentText = editingController.text;

    params.inputTextBehaviors.onTextChange(
      params.id,
      editingController.text,
    );

    widget.onParamsUpdated?.call(params);
  }

  @override
  bool get canEdit =>
      widget.params.permissions?.isFieldCanEdit(
          isCreatingTicket: widget.isCreatingTicket, hasValue: hasInitData) ==
      true;

  void onTextFieldFocus() {
    // widget.inputBehavior.onFocus();
    if (!canEdit) return;

    if (inputNumberFocusNode.hasFocus) {
      Future.delayed(const Duration(milliseconds: 150)).then((value) {
        _rxKeyboardVisible.value = true;
        params.rxHasFocus.value = true;
        showModalBottomSheet(
          context: context,
          barrierColor: Colors.transparent,
          isScrollControlled: true,
          isDismissible: true,
          backgroundColor: Colors.transparent,
          builder: (context) {
            return Container(
              color: Colors.transparent,
              width: double.infinity,
              height: MediaQuery.of(context).copyWith().size.height * 2,
              child: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  hideKeyboard();
                },
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: GPNumbericKeyboard(
                    editingController: editingController,
                    params: keyboardParams,
                  ),
                ),
              ),
            );
          },
        );
      });
    } else {}
  }

  @override
  void hideKeyboard() {
    Navigator.pop(context);
    FocusManager.instance.primaryFocus?.unfocus();
    // widget.inputBehavior.onFocus();
    inputNumberFocusNode.unfocus();

    _rxKeyboardVisible.value = false;
    params.rxHasFocus.value = false;
  }

  @override
  bool hasData(BaseTicketInputValidateMode mode) {
    if (widget.params.permissions?.canSubmitRequireField == true) {
      return true;
    }
    return params.isRequired == true
        ? _formKey.currentState?.validate() ?? true
        : true;
  }

  Future pickCurrency() async {
    final result = await Popup.instance.showBottomSheet(
      TicketPickSelectionBottomSheet(
        params: TicketPickSelectionParams.pickCurrency(
          currentSelected: rxCurrencyEntity.value,
          inputData: (params.currencies ?? [])
              .map((e) => e.toBottomSheetActionModel(
                    currentSelected: rxCurrencyEntity.value,
                  ))
              .toList(),
          onCompareTextChanged: (entity, searchStr) {
            return entity.onCompareSearchText(searchStr);
          },
        ),
      ),
    );

    if (result != null && result is TicketSelectionSheetResult) {
      rxCurrencyEntity.value = result.selection;
      params.currentCurrency = result.selection;
    }
  }

  late final CurrencyTextInputFormatter format =
      CurrencyTextInputFormatter.currency(
    symbol: '',
    enableNegative: true,
    decimalDigits: params.keepDecimalPlaces,
  );

  String parseANumber(double? number) {
    String inputNumber =
        number?.toStringAsFixed(params.keepDecimalPlaces) ?? '';

    return format.formatString(inputNumber);
  }

  String? get helperText {
    final hasMin = params.minValue != null;
    final hasMax = params.maxValue != null;
    if (!hasMin && !hasMax) {
      return null;
    }

    final min = parseANumber(params.minValue);
    final max = parseANumber(params.maxValue);

    if (hasMin && hasMax) {
      return LocaleKeys.ticket_create_input_number_validator_range.tr.format([
        min,
        max,
      ]);
    }

    if (hasMin) {
      return LocaleKeys.ticket_create_input_number_validator_min.tr.format([
        min,
        max,
      ]);
    }

    if (hasMax) {
      return LocaleKeys.ticket_create_input_number_validator_max.tr.format([
        max,
      ]);
    }

    return null;
  }

  String? validatorNumber(String? input) {
    if ((input == null || input.isEmpty) && params.isRequired) {
      return LocaleKeys.ticket_create_input_number_validator_empty.tr;
    }

    double? parseNum = double.tryParse(input?.replaceAll(',', '') ?? '');
    final minValue = params.minValue;
    final maxValue = params.maxValue;

    if (parseNum == null && (input?.isNotEmpty ?? false)) {
      return LocaleKeys.ticket_create_input_number_validator_format.tr;
    }

    if (parseNum == null) return null;

    if (minValue != null) {
      if (parseNum < minValue) {
        return LocaleKeys.ticket_create_input_number_validator_min.tr.format(
          [parseANumber(minValue), parseANumber(maxValue)],
        );
      }
    }

    if (maxValue != null) {
      if (parseNum > maxValue) {
        return LocaleKeys.ticket_create_input_number_validator_max.tr.format(
          [parseANumber(maxValue)],
        );
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return widget.params.permissions?.canView == true
        ? AutoScrollTag(
            key: ValueKey(params.formIndex),
            controller: ticketAutoScrollController,
            index: params.formIndex,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TitleWidget(
                  title: widget.params.title,
                  subTitle: widget.params.subTitle,
                ),
                const SizedBox(height: 8),
                IgnorePointer(
                  ignoring: !canEdit,
                  child: Form(
                    key: _formKey,
                    child: ValueListenableBuilder(
                      valueListenable: params.rxHasFocus,
                      builder: (context, value, child) {
                        return TextFormField(
                          autofocus: false,
                          controller: editingController,
                          enabled: params.enabled,
                          focusNode: inputNumberFocusNode,
                          textCapitalization: TextCapitalization.sentences,
                          style: textStyle(GPTypography.bodyLarge)
                              ?.merge(TextStyle(color: GPColor.contentPrimary)),
                          cursorColor: GPColor.functionAccentWorkSecondary,
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          maxLength: params.maxLength,
                          minLines: 1,
                          maxLines: params.maxLines,
                          maxLengthEnforcement: MaxLengthEnforcement.enforced,
                          keyboardType: params.textInputType,
                          validator: params.validator ?? validatorNumber,
                          textAlignVertical: TextAlignVertical.center,
                          decoration: InputDecoration(
                            helper: helperText != null
                                ? InkWell(
                                    onTap: () => hideKeyboard(),
                                    highlightColor: Colors.transparent,
                                    splashColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    child: Text(
                                      helperText ?? '',
                                      style: textStyle(GPTypography.bodySmall)
                                          ?.mergeColor(
                                              GPColor.contentSecondary),
                                    ),
                                  )
                                : const SizedBox(),
                            // helperText: helperText,
                            suffixIcon: _buildSuffixWidget(),
                            isDense: true,
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: GPColor.functionAccentWorkSecondary,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: value
                                    ? GPColor.functionAccentWorkSecondary
                                    : GPColor.linePrimary,
                              ),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: value
                                    ? GPColor.functionAccentWorkSecondary
                                    : GPColor.linePrimary,
                              ),
                            ),
                            contentPadding:
                                const EdgeInsets.fromLTRB(12, 10, 12, 10),
                            hintText: params.hint ?? '',
                            hintStyle: textStyle(GPTypography.bodyLarge)
                                ?.mergeColor(GPColor.contentQuaternary),
                          ),
                          enableInteractiveSelection: false,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ).paddingSymmetric(horizontal: 16, vertical: 8)
        : const SizedBox();
  }

  Widget _buildSuffixWidget() {
    return Container(
      padding: const EdgeInsets.only(right: 8),
      constraints: const BoxConstraints(maxWidth: 120),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (params.unitStr?.isNotEmpty == true) const SizedBox(width: 8),
          if (params.unitStr?.isNotEmpty == true)
            Text(
              params.unitStr ?? '',
              style: textStyle(GPTypography.bodyLarge)
                  ?.mergeColor(GPColor.contentSecondary),
            ),
          if (params.unitStr?.isNotEmpty == true) const SizedBox(width: 8),
          // params.showClearBtn
          //     ? ValueListenableBuilder(
          //         valueListenable: rxHasText,
          //         builder: (context, value, child) {
          //           return InkWell(
          //             customBorder: const CircleBorder(),
          //             onTap: _onClearText,
          //             child: Padding(
          //               padding: const EdgeInsets.only(top: 4.0),
          //               child: SvgWidget(
          //                 Assets
          //                     .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_XMARK_CIRCLE_SVG,
          //                 width: 20,
          //                 height: 20,
          //                 color: GPColor.contentSecondary,
          //               ),
          //             ),
          //           );
          //         },
          //       )
          //     : const SizedBox(),
          if (rxCurrencyEntity.value != null && params.currencies != null) ...{
            const SizedBox(width: 8),
            ValueListenableBuilder(
              valueListenable: rxCurrencyEntity,
              builder: (context, value, child) {
                return InkWell(
                  onTap: () => pickCurrency(),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: 6, horizontal: 2),
                    child: Row(
                      children: [
                        Text(
                          value?.code ?? '',
                          style: textStyle(GPTypography.bodyLarge)
                              ?.mergeColor(GPColor.contentSecondary),
                        ),
                        SvgWidget(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_ARROWHEAD_DOWN_SVG,
                          color: GPColor.contentSecondary,
                        )
                      ],
                    ),
                  ),
                );
              },
            )
          }
        ],
      ),
    );
  }
}

class _TicketCreateInputNumberReadOnlyWidget<
    P extends TicketCreateInputNumberParams> extends StatelessWidget {
  const _TicketCreateInputNumberReadOnlyWidget({
    required this.params,
    super.key,
  });

  final P params;

  @override
  Widget build(BuildContext context) {
    final String currentValueStr = params
        .parseCurrentValueStr(params.workFlowFormFieldEntity.currentText)
        .toString();
    final isEmpty = params.workFlowFormFieldEntity.currentText.isEmpty;
    return params.permissions?.canView == true
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TitleWidget(title: params.title, subTitle: params.subTitle),
              const SizedBox(height: 8),
              isEmpty
                  ? const SizedBox()
                  : Text(
                      '$currentValueStr ${params.unitStr} ${params.displayCurrentCode}',
                      style: textStyle(GPTypography.bodyLarge),
                    ),
            ],
          ).paddingOnly(bottom: 24, left: 16, right: 16)
        : const SizedBox();
  }
}

final ValueNotifier<bool> _rxKeyboardVisible = ValueNotifier(false);

class GPNumbericSoftKeyboard extends StatelessWidget {
  const GPNumbericSoftKeyboard({
    this.scrollController,
    super.key,
  });

  final ScrollController? scrollController;

  static const kKeyboardHeight = 270.0;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _rxKeyboardVisible,
      builder: (context, value, child) {
        if (value) {
          scrollController?.animateTo(
            (scrollController?.offset ?? 0) + kKeyboardHeight,
            duration: const Duration(milliseconds: 100),
            curve: Curves.fastOutSlowIn,
          );
          return const SizedBox(height: kKeyboardHeight);
        }

        return const SizedBox();
      },
    );
  }
}
