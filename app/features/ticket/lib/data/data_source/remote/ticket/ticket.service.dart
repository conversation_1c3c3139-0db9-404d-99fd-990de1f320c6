/*
 * Created Date: Thursday, 4th July 2024, 16:24:21
 * Author: gapo
 * -----
 * Last Modified: Friday, 11th April 2025 16:39:20
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core/base/models/list_api_response.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/cancel/ticket_cancel.params.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/details/tag/ticket_tag.params.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../../../../constants/url/ticket/ticket_url.constants.dart';
import '../../../model/model.dart';

part 'ticket.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kTicketService')
@RestApi()
abstract class TicketService {
  @FactoryMethod()
  factory TicketService(
    @Named('kDio') Dio dio, {
    @Named('kTicketUrl') String? baseUrl,
  }) = _TicketService;

  @GET(TicketUrlConstants.kTicketEsUrl)
  Future<ListAPIResponseV2<TicketListResponse>> tickets({
    @Queries() required TicketListParams params,
  });

  @GET('${TicketUrlConstants.kTicketUrl}/{id}')
  Future<ApiResponseV2<TicketListResponse>> details({
    @Path('id') required String ticketId,
  });

  @GET(TicketUrlConstants.kTicketEsUrl)
  Future<ListAPIResponseV2<TicketListResponse>> relativeTickets({
    @Queries() required TicketListRelativeParams params,
  });

  @POST(TicketUrlConstants.kTicketUrl)
  @Headers({
    HttpKeyConstants.kContentType: HttpKeyConstants.kApplicationJson,
  })
  Future<ApiResponseV2<TicketListResponse>> createTickets({
    @Body() required TicketCreateParams params,
  });

  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kFlowChartUrl}')
  Future<ApiResponseV2<TicketFlowChartResponse>> flowCharts({
    @Path('ticketId') required String ticketId,
  });

  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}')
  Future<ApiResponseV2<TicketNodeResponse>> nodes({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
  });

  /// update người thực hiện
  @PATCH(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kAssigneeUrl}')
  Future<ApiResponseV2<dynamic>> assignee({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body(nullToAbsent: true) required TicketUpdateAssigneeParams params,
  });

  /// update người theo dõi
  @PATCH(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kFollowerUrl}')
  Future<ApiResponseV2<dynamic>> followers({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketFollowerParams params,
  });

  /// update người theo dõi cho tất cả các bước
  @POST('${TicketUrlConstants.kTicketUrl}/{ticketId}/follower')
  Future<void> addFollowersAllStep({
    @Path('ticketId') required String ticketId,
    @Body() required TicketFollowerParams params,
  });

  /// get additional request
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kAdditionalRequestsUrl}')
  Future<ListAPIResponseV2<TicketAdditionalRequestResponse>>
      getAdditionalRequest({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
  });

  /// get activities
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kActivityRequestsUrl}')
  Future<ListAPIResponseV2<TicketActivityResponse>> getActivities(
      {@Path('ticketId') required String ticketId});

  /// get activity detail
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kActivityRequestsUrl}/{activityId}')
  Future<ApiResponseV2<TicketActivityResponse>> getActivityDetail({
    @Path('ticketId') required String ticketId,
    @Path('activityId') required String activityId,
  });

  /// Yêu cầu bổ sung thông tin
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kAdditionalRequestsUrl}')
  Future<ApiResponseV2<dynamic>> additionalRequests({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketAdditionalRequestsParams params,
  });

  /// Phản hồi yêu cầu bổ sung thông tin
  @PATCH(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kAdditionalRequestsUrl}/{additionalRequestId}${TicketUrlConstants.kResponseAdditionalRequestsUrl}')
  Future<ApiResponseV2<TicketAdditionalRequestResponse>>
      responseAdditionalRequests({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Path('additionalRequestId') required String additionalRequestId,
    @Body() required TicketResponseAdditionalRequestParams params,
  });

  /// Chuyển bước sau
  @PATCH(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kStatusUrl}')
  Future<ApiResponseV2<TicketNodeResponse>> status({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketUpdateNodeStatusParams params,
  });

  /// sửa ticket fields
  @PATCH(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kFieldsUrl}')
  Future<void> fields({
    @Path('ticketId') required String ticketId,
    @Body() required TicketUpdateFieldValuesParams params,
  });

  /// spam
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kSpamReportsUrl}')
  Future<ApiResponseV2<dynamic>> spam({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketSpamParams params,
  });

  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kSpamReportsUrl}/{id}')
  Future<ApiResponseV2<TicketSpamRequestResponse>> getSpam({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Path('id') required String id,
  });

  /// move to onhold
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kOnHoldUrl}')
  Future<ApiResponseV2<dynamic>> moveToOnHold({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketMoveToOnHoldParams params,
  });

  /// get onhold request
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kOnHoldUrl}')
  Future<ListAPIResponseV2<TicketOnHoldRequestResponse>> getOnHoldRequest({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
  });

  /// accept onhold request
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kOnHoldUrl}/{onHoldId}${TicketUrlConstants.kAcceptOnHoldUrl}')
  Future<void> acceptOnHold({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Path('onHoldId') required String onHoldId,
  });

  /// reject onhold request
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kOnHoldUrl}/{onHoldId}${TicketUrlConstants.kRejectOnHoldUrl}')
  Future<void> rejectOnHold({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Path('onHoldId') required String onHoldId,
  });

  /// cancel onhold request
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kOnHoldUrl}/{onHoldId}${TicketUrlConstants.kCancelOnHoldUrl}')
  Future<void> cancelOnHold({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Path('onHoldId') required String onHoldId,
  });

  @GET(TicketUrlConstants.kMembers)
  Future<ListAPIResponse<Assignee>> members({
    @Queries() required Map<String, dynamic> membersParams,
  });

  @GET(
      '${TicketUrlConstants.kTicketUrl}/{id}${TicketUrlConstants.kFollowMembers}')
  Future<ListAPIResponse<Assignee>> getFollowMembers({
    @Path('id') required String ticketId,
    @Queries() required TicketFollowMembersParams params,
  });

  /// get comments
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kCommentsUrl}')
  Future<ListAPIResponseV2<TicketCommentResponse>> comments({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Queries() required TicketCommentsParams params,
  });

  @POST('${TicketUrlConstants.kTicketUrl}/${TicketUrlConstants.kCommentsUrl}')
  Future<ApiResponseV2<TicketCommentResponse>> postComment({
    @Body(nullToAbsent: true) required TicketPostCommentsRequestParams params,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kUnfollowUrl}')
  Future<void> unfollow({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
  });

  @DELETE('${TicketUrlConstants.kTicketUrl}/{ticketId}')
  Future<void> deleteTicket({
    @Path('ticketId') required String ticketId,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kReopenUrl}')
  Future<void> reopen({
    @Path('ticketId') required String ticketId,
    @Body() required TicketReopenParams params,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kCloseUrl}')
  Future<void> close({
    @Path('ticketId') required String ticketId,
    @Body() required Map<String, dynamic> params,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kReviewUrl}')
  Future<ApiResponseV2<TicketListResponse>> review({
    @Path('ticketId') required String ticketId,
    @Body() required TicketReviewParams params,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kCancelUrl}')
  Future<void> cancel({
    @Path('ticketId') required String ticketId,
    @Body() required TicketCancelParams params,
  });

  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kLabelUrl}')
  Future<void> labels({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketLabelParams params,
  });

  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kIsAssigneeUrl}')
  Future<ApiResponseV2<TicketIsTicketAssigneeResponse>> isAssignee({
    @Path('ticketId') required String ticketId,
  });

  /// Get nodes that can be redone
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kCanRedoNodesUrl}')
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoNodes({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
  });

  /// Get end node that can be redone
  @GET(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kCanRedoEndNodeUrl}')
  Future<ListAPIResponseV2<TicketNodeResponse>> getCanRedoEndNode({
    @Path('ticketId') required String ticketId,
  });

  /// Redo end nodes
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kRedoEndNodesUrl}')
  Future<void> redoEndNodes({
    @Path('ticketId') required String ticketId,
    @Body() required TicketRedoEndNodeParams params,
  });

  /// Redo previous steps
  @POST(
      '${TicketUrlConstants.kTicketUrl}/{ticketId}${TicketUrlConstants.kNodeUrl}/{nodeId}${TicketUrlConstants.kRedoPreviousUrl}')
  Future<void> redoPrevious({
    @Path('ticketId') required String ticketId,
    @Path('nodeId') required String nodeId,
    @Body() required TicketRedoPreviousParams params,
  });
}
