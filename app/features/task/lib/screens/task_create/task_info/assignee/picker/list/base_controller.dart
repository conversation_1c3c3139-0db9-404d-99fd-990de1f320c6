import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/service/task_api.dart';

class AssigneePickerBaseController extends BaseListController<Assignee> {
  AssigneePickerBaseController({
    required this.assigneeArguments,
    required this.isEditMode,
    required this.projectId,
    GPConnection? gpConnection,
  }) : super(gpConnection ?? GPConnectionConcrete()) {
    debounceSearch =
        debounce(searchStr, onSearchBarSubmit, time: 400.milliseconds);

    selectedAssignees.clear();
    selectedAssignees.addAll(assigneeArguments);

    _initUserMe();

    _swapUserMe();

    getListItems();
  }

  final String? projectId;
  final List<Assignee> assigneeArguments;
  final RxBool isEditMode;

  final AssigneeApi api = AssigneeApi();
  final TaskAPI taskAPI = TaskAPI();

  final TextEditingController textEditingController = TextEditingController();

  // chứa những items user đã pick (selected = true)
  final RxList<Assignee> selectedAssignees = RxList<Assignee>();

  // use this string instead of searchController.text
  final RxString searchStr = "".obs;

  late Worker debounceSearch;

  // hard code current user = login user
  late Assignee userMe;

  @override
  bool get isSuccessState =>
      isEditMode.value ? listItem.isNotEmpty : selectedAssignees.isNotEmpty;

  @override
  void onClose() {
    debounceSearch.dispose();
    super.onClose();
  }

  @override
  Future getListItems() async {
    super.getListItems();

    try {
      ListAPIResponse<Assignee> response;
      if (projectId != null) {
        if (searchStr.value.isEmpty) {
          // TODO: fix search tạm thời do limit, cần api search
          // 04/03/2025 ToanNM đổi từ 1000 -> 100, chưa hiểu sao trước chơi máu vậy: 1000 @@
          response = await taskAPI.getProjectMembers(
              projectId: projectId, limit: 100, nextLink: nextLink);
          projectMemberBackup = response;
        } else {
          response = searchProjectMemberLocal(searchStr.value);
        }
      } else {
        response = await api.getAssignees(
          q: searchStr.value,
          nextLink: nextLink,
          onlyCurrentWorkspace: true,
        );
      }

      //response = await api.getAssignees(q: searchStr.value, nextLink: nextLink);

      if (response.data?.isEmpty == true) {
        handleResponse([], response.links?.next);
        showEmptyState.value = true;
        listItem.clear();
      } else {
        _removeDuplicateUserme(response.data!);

        _removeDisabledUser(response.data!);

        handleResponse(response.data!, response.links?.next);

        _syncList(assignees: listItem, selectedAssignees: selectedAssignees);

        _addUserMe();
      }
    } catch (error, s) {
      handleError(error, s);
    }
  }

  // @override
  // void handleError(Object error) {
  //   if (listItem.isEmpty) {
  //     if (error is AppException &&
  //         error.message != null &&
  //         error.message!.isNotEmpty) {
  //       try {
  //         var _map = jsonDecode(error.message!);
  //         errorStr.value = _map["message"];
  //       } catch (e) {
  //         errorStr.value = error.message!;
  //       }
  //     } else {
  //       errorStr.value = error.toString();
  //     }
  //   }
  // }

  void onSearchBarSubmit(String searchStr) {
    this.searchStr.value = searchStr;
    reload();
  }

  /// Cập nhật lại status selected giữa 2 list [assignees] và [selectedAssignees]
  void _syncList({
    required List<Assignee> assignees,
    required List<Assignee> selectedAssignees,
  }) {
    //sync executors
    for (var selectedElement in selectedAssignees) {
      assignees
          .firstWhere((element) => element.id == selectedElement.id,
              // orElse to cancel throwing error
              orElse: () => selectedElement)
          .isSelected
          .value = selectedElement.isSelected.value;
    }
  }

  void _initUserMe() {
    userMe = Assignee(
      id: int.parse(Constants.userId()),
      displayName: LocaleKeys.task_assignee_assignMe.tr,
      avatar: Constants.avatar(),
      avatarThumbPattern: Constants.avatar(),
    );

    if (selectedAssignees.isNotEmpty) {
      // cập nhật lại trạng thái select userMe trước đó
      userMe.isSelected.value = selectedAssignees
          .firstWhere((element) => element.id == userMe.id,
              orElse: () => Assignee(id: -1, displayName: ''))
          .isSelected
          .value;
    }
  }

  void _addUserMe() {
    /// thêm [userMe] lên trên cùng, không áp dụng khi search
    if (searchStr.isEmpty) {
      if (listItem.isEmpty ||
          (listItem.isNotEmpty && listItem.first.id != userMe.id)) {
        listItem.remove(userMe);
        listItem.insert(0, userMe);
      }
    }
  }

  /// swap userMe lên đầu tiên nếu userMe tồn tại trong [selectedAssignees]
  void _swapUserMe() {
    if (selectedAssignees.isEmpty) {
      return;
    }

    if (selectedAssignees.first.id == userMe.id) {
      return;
    }

    Assignee? temp;
    for (int index = 1; index < selectedAssignees.length; index++) {
      if (selectedAssignees[index].id == userMe.id) {
        temp = selectedAssignees[index];
        break;
      }
    }

    if (temp != null) {
      bool removed = selectedAssignees.remove(temp);
      if (removed) {
        selectedAssignees.insert(0, temp);
      }
    }
  }

  void _removeDuplicateUserme(List<Assignee> data) {
    if (searchStr.isEmpty) {
      data.removeWhere((element) => element.id == userMe.id);
    }
  }

  void _removeDisabledUser(List<Assignee> data) {
    data.removeWhere((element) => element.isDisabledUser);
  }

  void add(Assignee assignee) {
    // add exits assignee or add new assignee
    Assignee tempAssignee =
        selectedAssignees.firstWhere((element) => element.id == assignee.id,
            // can not find assignee, so add new assignee to selected list
            orElse: () => assignee);
    selectedAssignees.add(tempAssignee);

    if (userMe.id == assignee.id) {
      userMe.isSelected.value = assignee.isSelected.value;
    }
  }

  void remove(Assignee assignee) {
    // remove exits assignee or already assignee in selected list
    Assignee tempAssignee =
        selectedAssignees.firstWhere((element) => element.id == assignee.id,
            // can not find assignee, so remove exist assignee
            orElse: () => assignee);
    listItem
        .firstWhere((element) => element.id == assignee.id,
            // orElse to cancel throwing error
            orElse: () => assignee)
        .isSelected
        .value = assignee.isSelected.value;
    selectedAssignees.remove(tempAssignee);

    if (userMe.id == assignee.id) {
      userMe.isSelected.value = assignee.isSelected.value;
    }
  }

  ListAPIResponse<Assignee>? projectMemberBackup;

  ListAPIResponse<Assignee> searchProjectMemberLocal(String keyword) {
    if (keyword.isNotEmpty) {
      List<Assignee>? result = (projectMemberBackup?.data ?? [])
          .where((element) => (TiengViet.parse(element.displayName))
              .toLowerCase()
              .contains(TiengViet.parse(keyword).toLowerCase()))
          .toList();
      return ListAPIResponse(
        code: 200,
        data: result,
        message: "success",
        links: Links(next: "", prev: "", totalPages: 1),
      );
    } else {
      return projectMemberBackup ??
          ListAPIResponse(
            code: 200,
            data: [],
            message: "success",
            links: Links(next: "", prev: "", totalPages: 0),
          );
    }
  }
}
