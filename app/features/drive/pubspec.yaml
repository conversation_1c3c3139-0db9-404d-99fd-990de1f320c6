name: gp_feat_drive
version: 0.0.1
publish_to: none
description: A new Flutter package project.
environment:
  sdk: ">=3.6.0 <4.0.0"
  flutter: 3.29.1
resolution: workspace

# dependency_overrides:
#   intl: ^0.19.0
#   device_info_plus: ^10.1.0
#   http: ^1.0.0
dependencies:
  auto_mappr_annotation: ^2.1.0
  dio: ^5.4.0
  flutter_bloc: ^8.1.3
  freezed_annotation: ^2.4.1
  get_it: ^7.6.4
  go_router: ^14.2.0
  injectable: ^2.3.5
  json_annotation: ^4.9.0
  retrofit: ^4.0.3
  talker: ^4.2.0
  talker_flutter: ^4.1.2
  talker_bloc_logger: ^4.2.0
  flutter:
    sdk: flutter
  gp_core:
    git:
      url: **********************:flutter/core/gp_core.git
      ref: "develop"
  gp_core_v2:
    git:
      url: **********************:flutter/core/gp_core_v2.git
      ref: "develop"
      path: gp_core_v2
  gp_shared:
    git:
      url: **********************:flutter/components/gp_shared.git
      ref: "develop"
  downloadsfolder:
    git:
      url: **********************:flutter/components/gp_downloadsfolder.git
      ref: "develop"

  flutter_fancy_tree_view: ^1.4.1

dev_dependencies:
  auto_mappr: ^2.2.0
  build_runner: ^2.4.7
  flutter_lints: ^4.0.0
  freezed: ^2.4.6
  go_router_builder: ^2.4.1
  injectable_generator: ^2.6.1
  json_serializable: ^6.7.1
  mock_web_server: ^5.0.0-nullsafety.1
  pubspec_dependency_sorter: ^1.0.4
  retrofit_generator: ^8.0.6
  flutter_test:
    sdk: flutter
  gp_analysis:
    git:
      url: **********************:flutter/core/gp_analysis.git
      ref: develop
