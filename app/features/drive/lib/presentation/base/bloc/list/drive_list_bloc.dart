/*
 * Created Date: 1/02/2024 11:29:52
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 7th March 2024 14:20:09
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core_v2/base/base.dart';

import '../../../../data/data.dart';
import '../../../../domain/entity/drive.dart';
import '../../../presentation.dart';

mixin DriveListBehaviorMixin<T, R> implements DriveListBehavior<T, R> {
  @override
  Future<R?>? usecaseOnSearchData({
    required DriveSearchFileParams params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
  }) async {
    throw UnimplementedError('usecaseOnSearch');
  }
}

/// Các behavior của list
abstract class DriveListBehavior<T, R> {
  /// usecase when user load data, refresh data, loadMore data
  Future<R?>? usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
  });

  /// usecase when user search
  Future<R?>? usecaseOnSearchData({
    required DriveSearchFileParams params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
  });

  /// emit [DriveListDataLoaded] khi có data
  /// extends for each case is needed
  Future<DriveListDataLoaded<T>> emitData({
    required R response,
    required Emitter<DriveListState> emit,
    required bool isInitialLoad,
  });
}

/// Quản lý các action của
abstract class DriveListBloc<T, R>
    extends CoreV2BaseBloc<CoreV2BaseEvent, DriveListState> {
  DriveListBloc() : super(const DriveListState()) {
    on<DriveListEvent>(_onDriveListEvent);

    on<DriveListUpdateDataEvent<T>>(_onDriveListUpdateDataEvent);

    on<DriveListChangeModeEvent>(_onDriveListChangeModeEvent);

    on<DriveListItemOnCheckedChangedEvent>(
        _onDriveListItemOnCheckedChangedEvent);
  }

  DriveListBehavior<T, R>? _behavior;

  DriveListDataLoaded? _firstPageDataLoadedState;

  dynamic _inputData;

  String? _nextLink = '';

  FolderEntity? currentFolder;

  final List<T> _loadedEntities = [];
  final Map<int, T> _selectedEntities = {};

  void setOnUseCaseBehavior(DriveListBehavior<T, R> behavior) {
    this._behavior = behavior;
  }

  void initInputData(dynamic inputData) {
    _inputData = inputData;
  }

  void loadData({
    bool isInitialLoad = true,
    dynamic inputData,
  }) {
    add(
      DriveListEvent(
        isInitialLoad: isInitialLoad,
        nextLink: isInitialLoad ? '' : _nextLink,
        inputData: inputData ?? _inputData,
      ),
    );
  }

  void searchData({
    required DriveSearchFileParams searchParams,
    bool isInitialLoad = true,
    dynamic inputData,
  }) {
    add(
      DriveListEvent(
        isInitialLoad: isInitialLoad,
        nextLink: isInitialLoad ? '' : _nextLink,
        searchParams: searchParams,
        inputData: inputData ?? _inputData,
      ),
    );
  }

  FutureOr _onDriveListEvent(
    DriveListEvent event,
    Emitter<DriveListState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      doOnError: (error, stackTrace) async {
        logE('_onDriveListEvent error $error');
        emit(
          DriveListHasError(
            errror: error,
          ),
        );

        onError(error, StackTrace.fromString(error.toString()));
      },
      action: () async {
        assert(_behavior != null, "DriveListBehavior is not setup");

        final List<R> response = [];

        final bool isSearch = event.searchParams != null;

        final bool isRefreshData = event.isInitialLoad;

        if (isSearch) {
          // searchString is empty -> return state ở page 1
          if (_searchChecker(event.searchParams!.search)) {
            if (_firstPageDataLoadedState != null) {
              emit(_firstPageDataLoadedState!);
            }
            return;
          }

          R? result = await _behavior!.usecaseOnSearchData(
            params: event.searchParams!,
            isRefreshData: isRefreshData,
            inputData: event.inputData,
            nextLink: isRefreshData ? '' : _nextLink,
          );

          assert(result != null);

          if (result != null) {
            response.clear();
            response.add(result);
          }
        } else {
          response.clear();
          final result = await _behavior!.usecaseOnLoadData(
            isRefreshData: isRefreshData,
            inputData: event.inputData,
            nextLink: isRefreshData ? '' : _nextLink,
          );
          if (result != null) {
            response.add(result);
          } else {
            emit(
              DriveMyListDataLoaded<T>(
                isInitialLoad: isRefreshData,
                data: null,
                canNextPage: false,
                nextLink: null,
                currentFolder: currentFolder,
              ),
            );
          }
        }

        if (response.isEmpty) {
          return;
        }

        final state = await _behavior!.emitData(
          response: response.first,
          emit: emit,
          isInitialLoad: isRefreshData,
        );

        _nextLink = state.nextLink;

        /* 
          Lưu lại state ở page đầu tiên,
          Khi search với String empty,
          emit lại state này, tránh phải load lại data 1 lần.
        */
        if (event.nextLink == null) {
          _firstPageDataLoadedState = state;
        }

        emit(DriveListModeChanged(mode: ListViewMode.normal));

        emit(state);

        addLoadedData(
          isRefreshData,
          state.data ?? [],
        );
      },
    );
  }

  FutureOr _onDriveListUpdateDataEvent(
    DriveListUpdateDataEvent<T> event,
    Emitter<DriveListState> emit,
  ) async {
    if (state is DriveListDataLoaded<T>) {
      final currentState = state as DriveListDataLoaded;

      emit(
        DriveMyListDataLoaded<T>(
          isInitialLoad: currentState.isInitialLoad,
          data: event.data,
          canNextPage: currentState.canNextPage,
          nextLink: currentState.nextLink,
          currentFolder: currentFolder,
        ),
      );
    } else {
      addLoadedData(
        true,
        event.data,
      );
    }
  }

  FutureOr _onDriveListChangeModeEvent(
    DriveListChangeModeEvent event,
    Emitter<DriveListState> emit,
  ) async {
    _selectedEntities.clear();

    if (event.mode == ListViewMode.normal) {
      _clearSelectLoadedData();
    }

    emit(DriveListModeChanged(mode: event.mode));
  }

  FutureOr _onDriveListItemOnCheckedChangedEvent(
    DriveListItemOnCheckedChangedEvent event,
    Emitter<DriveListState> emit,
  ) async {
    bool isChecked = event.isChecked;

    switch (event.changeCase) {
      case DriveListItemOnCheckedChangedCase.aCheckChanged:
        assert(event.entity != null);

        if (event.entity != null && event.position != null) {
          if (isChecked) {
            _selectedEntities[event.position!] = event.entity;
          } else {
            _selectedEntities.remove(event.position);
          }
        }
        final isSelectedAll =
            _selectedEntities.length == _loadedEntities.length;
        emit(
          DriveListItemOnCheckedChanged(
            entitiesByPosition: _selectedEntities,
            headerSelected: isSelectedAll
                ? true
                : _selectedEntities.isEmpty
                    ? false
                    : null,
          ),
        );
        break;
      case DriveListItemOnCheckedChangedCase.allCheckChanged:
        if (isChecked) {
          _selectedEntities.clear();

          for (int i = 0; i < _loadedEntities.length; i++) {
            final dynamic item = _loadedEntities[i];
            if (item is BaseFileEntity) {
              item.isSelected.value = true;
            }
            _selectedEntities[i] = item;
          }
        } else {
          for (int i = 0; i < _loadedEntities.length; i++) {
            final dynamic item = _loadedEntities[i];
            if (item is BaseFileEntity) {
              item.isSelected.value = false;
            }
          }

          _selectedEntities.clear();
        }
        emit(
          DriveListItemOnCheckedChanged(
            entitiesByPosition: _selectedEntities,
            headerSelected: isChecked,
          ),
        );
        break;
      default:
    }
  }

  bool _searchChecker(String search) {
    return search.isEmpty;
  }

  void addLoadedData(bool isInitialLoad, List<T> data) {
    if (isInitialLoad) {
      _loadedEntities.clear();
    }

    _loadedEntities.addAll(data);
  }

  void _clearSelectLoadedData() {
    if (_loadedEntities.isEmpty) return;

    if (_loadedEntities is List<BaseFileEntity>) {
      for (var element in (_loadedEntities as List<BaseFileEntity>)) {
        element.isSelected.value = false;
      }
    }
  }
}
