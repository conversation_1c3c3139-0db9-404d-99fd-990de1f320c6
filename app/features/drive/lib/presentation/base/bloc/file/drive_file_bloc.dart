/*
 * Created Date: 2/02/2024 21:05:35
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 8th March 2024 16:02:06
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/base.dart';
import 'package:gp_feat_drive/lib.dart';
import 'package:gp_shared/domain/usecase/media/media_preview.usecase.dart';
import 'package:injectable/injectable.dart';

/*
  Author: ToanNM
  Coding giai đoạn 01/2024-> 03/2024, khá <PERSON>c chế ở các phần này
  Ghi lại đây, sau làm bài học rút ra....
  1. Theo api, tách riêng toàn bộ file/folder, path riêng.
  2. Sau remake, no api docs, file/folder c<PERSON><PERSON> chung, c<PERSON><PERSON> r<PERSON>,
  dẫn tới phải thay đổi toàn bộ các tầng ở client:
    - data
    - domain
    - usecase
*/

/// Quản lý toàn bộ action liên quan tới file / folder / url / ....
@LazySingleton()
final class DriveFileBloc extends _DriveFileBloc
    with _BaseFileEventMixin, _FolderEventMixin, _FileEventMixin {
  DriveFileBloc(
    super.fileManagementDashBoardBloc,
  ) {
    // ---------- BASE FILE (COMMON) ---------- \\
    on<DriveBaseFileChangeState>(_onDriveBaseFileChangeState);

    on<DriveDeleteBaseFileEvent>(_onDriveDeleteBaseFile);

    on<DriveRecoveryBaseFileEvent>(_onDriveRecoveryBaseFile);

    on<DriveMoveBaseFileEvent>(_onDriveMoveBaseFile);

    on<DriveDeleteAllFile>(_onDriveDeleteAllFile);

    on<DriveDeleteSharedFileEvent>(_onDriveDeleteSharedFile);

    // ---------- FOLDER ---------- \\
    on<DriveCreateFolderEvent>(_onDriveCreateFolder);

    on<DriveRenameFolderEvent>(_onDriveRenameFolder);

    // ---------- FILE --------- \\
    on<DriveRenameFileEvent>(_onDriveRenameFile);

    on<DrivePreviewFileEvent>(_onDrivePreviewFile);

    on<DriveUploadEvent>(_onDriveUpload);

    on<DriveDownloadEvent>(_onDriveDownload);
  }
}

base mixin _BaseFileEventMixin on _DriveFileBloc {
  FutureOr _onDriveBaseFileChangeState(
    DriveBaseFileChangeState event,
    Emitter<CoreV2BaseState> emit,
  ) {
    emit(
      DriveEntitiesChanged(
        entities: event.entities,
        updateCase: event.changedCase,
        effectiveFolderId: event.folderId,
      ),
    );
  }

  FutureOr _onDriveDeleteBaseFile(
    DriveDeleteBaseFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveDeleteBaseFileUseCase.execute(
          DriveDeleteBaseFileInput(event.params),
        );

        if (response.isSuccess) {
          if (event.params.deleteType == DriveDeleteType.deleteTemporary) {
            commonBloc.add(
              MessageEmitted(
                message: '${LocaleKeys.drive_popup_snack_bar_delete.tr.format(
                  ['${event.entities.length}'],
                )} ',
                boldMessage: LocaleKeys.drive_tab_bin_drive.tr,
                undoTitle: LocaleKeys.drive_behavior_roll_back.tr.toUpperCase(),
                snackbarType: SnackbarType.success,
                undoCallback: () => _rollback(event),
              ),
            );

            // bắn sự kiện update lại item với sourceEffectiveFolderId
            if (event.sourceEffectiveFolderId != null) {
              emit(
                DriveEntitiesChanged(
                  entities: event.entities,
                  updateCase: DriveListChangedCase.created,
                  effectiveFolderId: event.sourceEffectiveFolderId ?? '',
                ),
              );
            }

            // bắn sự kiện cập nhật lại màn Bin
            emit(
              const DriveDeleteAllFileState(
                DriveDeleteAllFileStateCase.needReload,
              ),
            );
          } else if (event.params.deleteType ==
              DriveDeleteType.deletePermanent) {
            commonBloc.add(
              MessageEmitted(
                message: '${LocaleKeys.drive_popup_snack_bar_delete_all.tr} ',
                snackbarType: SnackbarType.success,
                undoCallback: () => _rollback(event),
              ),
            );
          }

          // bắn sự kiện update lại item ở toàn bộ màn hình
          emit(
            DriveEntitiesChanged(
              entities: event.entities,
              updateCase: DriveListChangedCase.deleted,
              effectiveFolderId: event.targetEffectiveFolderId,
            ),
          );
        } else {
          commonBloc.add(
            MessageEmitted(
              message: '${LocaleKeys.drive_popup_snack_bar_error_delete.tr} ',
              boldMessage: event.entities.first.title,
              snackbarType: SnackbarType.error,
            ),
          );
        }
      },
    );
  }

  FutureOr _onDriveMoveBaseFile(
    DriveMoveBaseFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveMoveBaseFileUseCase.execute(
          DriveMoveBaseFileInput(event.params),
        );

        if (response.isSuccess) {
          commonBloc.add(
            MessageEmitted(
              message: '${LocaleKeys.drive_popup_snack_bar_move.tr} ',
              undoTitle: LocaleKeys.drive_behavior_roll_back.tr.toUpperCase(),
              snackbarType: SnackbarType.success,
              undoCallback: () => _rollback(event),
            ),
          );

          // notify [event.entities] đã xóa ở source folder
          emit(
            DriveEntitiesChanged(
              entities: event.entities,
              updateCase: DriveListChangedCase.deleted,
              effectiveFolderId: event.targetEffectiveFolderId,
            ),
          );

          // notify [event.entities] đã di chuyển đến ở target folder
          emit(
            DriveEntitiesChanged(
              entities: event.entities,
              entitiesByPosition: event.entitiesByPosition,
              updateCase: DriveListChangedCase.created,
              effectiveFolderId: event.params.parentId,
            ),
          );
        } else {
          commonBloc.add(
            MessageEmitted(
              message: '${LocaleKeys.drive_popup_snack_bar_error_delete.tr} ',
              boldMessage: event.entities.first.title,
              snackbarType: SnackbarType.error,
            ),
          );
        }
      },
    );
  }

  FutureOr _onDriveDeleteAllFile(
    DriveDeleteAllFile event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveDeleteAllFileUseCase.execute(event.input);

        if (response.message == DriveUrlConstants.kBEDeleteAllSuccessMessage) {
          commonBloc.add(
            MessageEmitted(
              message: '${LocaleKeys.drive_popup_snack_bar_delete_all.tr} ',
              snackbarType: SnackbarType.success,
            ),
          );

          emit(
            const DriveDeleteAllFileState(
              DriveDeleteAllFileStateCase.deletedAll,
            ),
          );
        } else {
          commonBloc.add(
            MessageEmitted(
              message:
                  '${LocaleKeys.drive_popup_snack_bar_error_delete_all.tr} ',
              snackbarType: SnackbarType.error,
            ),
          );
        }
      },
    );
  }

  FutureOr _onDriveRecoveryBaseFile(
    DriveRecoveryBaseFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveRecoveryBaseFileUseCase.execute(
          DriveRecoveryBaseFileInput(
            DriveRecoveryBaseFileParams(
              recoveryIds: event.entities.toIds(),
            ),
          ),
        );

        final needShowSnackbar = event.showResultSnackBar;

        if (response.isSuccess) {
          if (needShowSnackbar) {
            commonBloc.add(
              MessageEmitted(
                message:
                    '${LocaleKeys.drive_popup_snack_bar_recovery_leading.tr} ',
                boldMessage: event.entities.first.title,
                snackbarType: SnackbarType.success,
              ),
            );
          }

          emit(
            DriveEntitiesChanged(
              entities: event.entities,
              updateCase: DriveListChangedCase.recovery,
              entitiesByPosition: event.entitiesByPosition,
              effectiveFolderId: event.targetEffectiveFolderId,
            ),
          );

          // bắn sự kiện update lại item với sourceEffectiveFolderId
          if (event.sourceEffectiveFolderId != null) {
            emit(
              DriveEntitiesChanged(
                entities: event.entities,
                updateCase: DriveListChangedCase.deleted,
                effectiveFolderId: event.sourceEffectiveFolderId ?? '',
              ),
            );
          }
        } else {
          if (needShowSnackbar) {
            commonBloc.add(
              MessageEmitted(
                message: '${LocaleKeys.drive_popup_snack_bar_error_delete.tr} ',
                boldMessage: event.entities.first.title,
                snackbarType: SnackbarType.error,
              ),
            );
          }
        }
      },
    );
  }

  Future _rollback(DriveBaseMultipleActionEvent event) async {
    await GPPopup.instance.closeSnackBar();

    add(
      DriveRecoveryBaseFileEvent(
        entities: event.entities,
        showResultSnackBar: false,
        entitiesByPosition: event.entitiesByPosition,
        targetEffectiveFolderId: event.targetEffectiveFolderId,
      ),
    );
  }
}

base mixin _FolderEventMixin on _DriveFileBloc {
  FutureOr _onDriveCreateFolder(
    DriveCreateFolderEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveAddFolderUseCase.execute(
          DriveAddFolderInput(event.params),
        );

        emit(
          DriveEntitiesChanged(
            entities: [convert<FolderResponse, FolderEntity>(response.data)],
            updateCase: DriveListChangedCase.created,
            effectiveFolderId: event.params.parentId,
          ),
        );
      },
    );
  }

  FutureOr _onDriveRenameFolder(
    DriveRenameFolderEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveRenameFolderUseCase.execute(
          DriveRenameFolderInput(event.params),
        );

        commonBloc.add(
          MessageEmitted(
            message: '${LocaleKeys.drive_file_management_rename_folder.tr} ',
            boldMessage: event.params.title,
            snackbarType: SnackbarType.success,
          ),
        );

        emit(
          DriveEntitiesChanged(
            entities: [
              convert<FolderResponse, FolderEntity>(response.data),
            ],
            updateCase: DriveListChangedCase.renamed,
            effectiveFolderId: event.parentFolderId,
          ),
        );
      },
    );
  }
}

base mixin _FileEventMixin on _DriveFileBloc {
  FutureOr _onDriveRenameFile(
    DriveRenameFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        final response = await driveRenameFileUseCase.execute(
          DriveRenameFileInput(event.params),
        );

        emit(
          DriveEntitiesChanged(
            entities: [
              convert<FileResponse, FileEntity>(response.data),
            ],
            updateCase: DriveListChangedCase.renamed,
            effectiveFolderId: event.parentFolderId,
          ),
        );
      },
    );
  }

  FutureOr _onDrivePreviewFile(
    DrivePreviewFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        await mediaPreviewUseCase.execute(event.params);
      },
    );
  }

  FutureOr _onDriveUpload(
    DriveUploadEvent event,
    Emitter<CoreV2BaseState> emit,
  ) async {
    final pickResult =
        await drivePickFileUseCase.execute(DrivePickFileInput(event.fileType));

    fileManagementDashBoardBloc.add(
      FileManagementDashBoardUploadEvent(
        folderEntity: event.folderEntity,
        filePickerResult: pickResult,
      ),
    );
  }

  FutureOr _onDriveDownload(
    DriveDownloadEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
      handleLoading: false,
      action: () async {
        fileManagementDashBoardBloc.add(
          FileManagementDashBoardDownloadEvent(
            entities: event.entities,
          ),
        );
      },
    );
  }

  FutureOr _onDriveDeleteSharedFile(
    DriveDeleteSharedFileEvent event,
    Emitter<CoreV2BaseState> emit,
  ) {
    return runCatching(
        handleLoading: false,
        action: () async {
          await driveShareRevokeUseCase.execute(ShareRevokeInput(
            params: DriveShareAddPermissionParams(
              itemId: event.entity.id,
              audiences: [
                DriveShareAddAudienceParams(
                    targetId: Constants.userId(),
                    targetType: DriveShareTargetType.personal,
                    role: DriveFileAccess.editor),
              ],
            ),
          ));
          emit(
            DriveEntitiesChanged(
              entities: [
                event.entity,
              ],
              updateCase: DriveListChangedCase.deleted,
              effectiveFolderId: event.sourceEffectiveFolderId ?? "",
            ),
          );
        },
        doOnError: (err, stackTrace) async {
          commonBloc.add(
            MessageEmitted(
              message: err.toString(),
              snackbarType: SnackbarType.error,
            ),
          );
        });
  }
}

abstract class _DriveFileBloc
    extends CoreV2BaseBloc<CoreV2BaseEvent, DriveFileState>
    with
        GPDriveMapperMixin,
        _CommonVariableMixin,
        _FolderVariableMixin,
        _FileVariableMixin {
  _DriveFileBloc(this.fileManagementDashBoardBloc)
      : super(const DriveFileState());

  final FileManagementDashBoardBloc fileManagementDashBoardBloc;
}

mixin _CommonVariableMixin {
  late final DriveDeleteBaseFileUseCase driveDeleteBaseFileUseCase =
      GetIt.I<DriveDeleteBaseFileUseCase>();

  late final DriveRecoveryBaseFileUseCase driveRecoveryBaseFileUseCase =
      GetIt.I<DriveRecoveryBaseFileUseCase>();

  late final DriveMoveBaseFileUseCase driveMoveBaseFileUseCase =
      GetIt.I<DriveMoveBaseFileUseCase>();

  late final DriveDeleteAllFileUseCase driveDeleteAllFileUseCase =
      GetIt.I<DriveDeleteAllFileUseCase>();

  late final DriveShareRevokeUseCase driveShareRevokeUseCase =
      GetIt.I<DriveShareRevokeUseCase>();
}

mixin _FolderVariableMixin {
  late final DriveAddFolderUseCase driveAddFolderUseCase =
      GetIt.I<DriveAddFolderUseCase>();

  late final DriveRenameFolderUseCase driveRenameFolderUseCase =
      GetIt.I<DriveRenameFolderUseCase>();
}

mixin _FileVariableMixin {
  late final MediaPreviewUseCase mediaPreviewUseCase =
      GetIt.I<MediaPreviewUseCase>();

  late final DriveRenameFileUseCase driveRenameFileUseCase =
      GetIt.I<DriveRenameFileUseCase>();

  late final DrivePickFileUseCase drivePickFileUseCase =
      GetIt.I<DrivePickFileUseCase>();
}
