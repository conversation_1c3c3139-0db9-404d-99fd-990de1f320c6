import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/lib.dart';

import '../../../../../data/model/response/file/directory_tree_response.dart';
import '../../../../../domain/entity/file/directory_tree.entity.dart';
import '../../../../../domain/usecase/load_data/drive_load_tree_directory.usecase.dart';
import '../../../../../mapper/gp_drive_mapper.dart';
import 'bloc.dart';

class DirectoryTreeListBloc
    extends CoreV2BaseBloc<DirectoryTreeListEvent, DirectoryTreeListState>
    with GPDriveMapperMixin {
  DirectoryTreeListBloc() : super(const DirectoryTreeListState()) {
    on<DirectoryTreeListEvent>(_onDirectoryListEvent);
  }

  final String _nextLink = '';

  final DriveLoadDirectoryTreeUseCase driveLoadDirectoryTreeUseCase =
      GetIt.I<DriveLoadDirectoryTreeUseCase>();

  FutureOr _onDirectoryListEvent(
    DirectoryTreeListEvent event,
    Emitter<DirectoryTreeListState> emit,
  ) async {
    return runCatching(
      handleLoading: true,
      doOnError: (error, stackTrace) async {
        logE('_onDriveListEvent error $error');
        emit(
          DirectoryTreeListError(error: error),
        );

        onError(error, StackTrace.fromString(error.toString()));
      },
      action: () async {
        final response = await driveLoadDirectoryTreeUseCase
            .execute(DriveLoadDirectoryTreeInput(_nextLink));

        final DirectoryTreeEntity entity =
            convert<DirectoryTreeResponse, DirectoryTreeEntity>(response);

        emit(
          DirectoryTreeListLoaded(
            data: entity.data,
          ),
        );
      },
    );
  }
}
