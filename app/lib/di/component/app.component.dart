/*
 * Created Date: 5/01/2024 17:22:23
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 21st April 2025 11:38:24
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ca/di/di.dart';
import 'package:gp_feat_coin/di/di.dart';
import 'package:gp_feat_drive/di/di.component.module.dart';
import 'package:gp_feat_portal/di/di.dart';
import 'package:gp_feat_ticket/di/di.dart';
import 'package:injectable/injectable.dart';

import 'app.component.config.dart';

final GetIt getIt = GetIt.instance;

@InjectableInit(
  externalPackageModulesBefore: [
    ExternalModule(GpCoreV2PackageModule),
  ],
  externalPackageModulesAfter: [
    ExternalModule(GpFeatCaPackageModule),
    ExternalModule(GpFeatTicketPackageModule),
    ExternalModule(GpFeatCoinPackageModule),
    ExternalModule(GpFeatPortalPackageModule),
    ExternalModule(GpFeatDrivePackageModule),
  ],
)
Future configureInjection({
  required Set<String> environmentFilters,
}) =>
    getIt.init(
      environmentFilter: NoEnvOrContainsAny(environmentFilters),
    );
