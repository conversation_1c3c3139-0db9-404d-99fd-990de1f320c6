/*
 * Created Date: Sunday, 20th April 2025, 13:20:57
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 17th July 2025 16:52:53
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter/services.dart';

String sentryFingerPrint = DateTime.now().toString();

abstract class GapoAppChannelBehavior {
  // ---------- implement on general ----------
  Future<bool> onSetLanguage(MethodCall call);

  Future<bool> onUpdateTokenInfo(MethodCall call, String currentRoute);
  
  Future<bool> onSetAccessToken(MethodCall call, String currentRoute);

  Future<bool> onUpdateUserInfo(MethodCall call);

  Future<bool> onSetAppInfo(MethodCall call, String currentRoute);

  Future<bool> onSetPlatformNavigatorParams(MethodCall call);

  Future<bool> onUpdateSwipeBackGestureIfNeeded(MethodCall call);

  Future<bool> onPop(MethodCall call);

  // ---------- implement on task ----------
  Future<bool> onSetTaskArguments(MethodCall call);

  Future<bool> onSetProject(MethodCall call, String currentRoute);

  Future<bool> onSetProjectArguments(MethodCall call, String currentRoute);

  Future<bool> onCreateTaskFromMessage(MethodCall call);

  Future<bool> onInitializeTaskCollab(MethodCall call, String currentRoute);

  Future<bool> onRefreshTaskCollab(MethodCall call);

  Future<bool> onInitTaskListData(MethodCall call);

  // ---------- implement on calendar ----------
  Future<bool> onSetEvent(MethodCall call);

  Future<bool> onRefreshEventList(MethodCall call);

  Future<bool> onRefreshOutlookSyncIfNeeded(MethodCall call);

  Future<bool> onLogout(MethodCall call);

  Future<bool> onInitializeCalendarCollab(MethodCall call);

  Future<bool> onScrollToTop(MethodCall call);

  Future<bool> onSyncSuccessfullys(MethodCall call);

  // ---------- implement on assignee picker ----------
  Future<bool> onInitializeAssigneePicker(MethodCall call);

  // ---------- implement on rtf editor ----------
  Future<bool> onInitializeRtfEditor(MethodCall call);

  // ---------- implement on rtf editor ----------
  Future<bool> onSetTicketArguments(MethodCall call);

  /// ---------- implement on caller ----------
  void onAppendError(String error);
}
