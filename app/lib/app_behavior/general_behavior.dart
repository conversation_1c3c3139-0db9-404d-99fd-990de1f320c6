/*
 * Created Date: Sunday, 20th April 2025, 13:57:58
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 17th July 2025 16:53:01
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:device_safety_info/device_safety_info.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gapo_flutter_app/loading_screen.dart';
import 'package:gapo_flutter_app/utils/route_to_interaction.dart';
import 'package:gp_core/base/networking/base/interceptors/token_interceptor.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/navigator/platform_navigator.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_event_type.dart';
import 'package:gp_feat_calendar/screens/detail/models/calendar_event_detail_input.dart';
import 'package:gp_feat_calendar/screens/list/models/calendar_collab_input.dart';
import 'package:gp_feat_calendar/screens/list/models/event_room.dart';
import 'package:gp_feat_coin/route/router_name.dart';
import 'package:gp_feat_portal/route/router_name.dart';
import 'package:gp_feat_task/models/section/section.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/task_create/nav/task_create_argument.dart';
import 'package:gp_native_shared/rtf_editor/routes/router_name.dart';

import '../main.dart';
import 'app_behavior.dart';

mixin GapoAppGeneralBehavior implements GapoAppChannelBehavior {
  bool isSetAppInfo = false;

  final List<AfterSetAppInfoCallBack> queues = [];

  @override
  Future<bool> onSetLanguage(MethodCall call) async {
    if (call.arguments is Map) {
      final language = call.arguments['language'] as String;

      if (language == Constants.language()) {
        //do nothing
      } else {
        Get.updateLocale(Locale(language.toLowerCase()));
      }
    }
    return true;
  }

  @override
  Future<bool> onUpdateTokenInfo(MethodCall call, String currentRoute) async {
    Map<String, dynamic> params = HashMap.from(call.arguments);
    final tokenInfo = TokenInfo.fromJson(params);
    await _updateTokenInfo(tokenInfo, currentRoute);

    // await initAppWithCoreV2();

    return true;
  }

  @override
  Future<bool> onSetAccessToken(MethodCall call, String currentRoute) async {
    String accessToken = call.arguments.toString();
    if (accessToken.isEmpty == true || accessToken == "null") {
      return false;
    }

    final renewTokenInfo = Constants.updateAccessToken(accessToken);
    await _updateTokenInfo(renewTokenInfo, currentRoute);

    /* 
    Flow:
      1. Khi flutter có api trả về 401, flutter pause toàn bộ request lên BE, flutter gọi method channel `renewToken` sang native.
      2. native xử lý renewToken.
      3. native call method `updateTokenInfo` sang flutter. Các phần retry khi renewToken, native sẽ xử lý: hiện tại native không xử lý retry, không renew được token sẽ force user logout.
      4. flutter xử lý token mới, tiếp tục xử lý requests.
      5. Timeout từ step 1 cho tới step 3 là 10s, sau 10s flutter sẽ hiển thị lỗi tương ứng.
    */
    try {
      onRenewTokenSuccess();
    } catch (e, s) {
      /// Chú ý: không xử lý `onRenewTokenFailure` ở đây để tránh:
      /// handler has been already called
      // onRenewTokenFailure();

      GPCoreTracker().appendError(
        'Flutter:core.renewToken.error: exception: $e\n stackTrace: $s',
      );
    }

    // await initAppWithCoreV2();

    return true;
  }

  @override
  Future<bool> onUpdateUserInfo(MethodCall call) async {
    Map<String, dynamic> params = HashMap.from(call.arguments);
    final tokenInfo = TokenInfo.fromJson(params);
    await _updateUserInfo(tokenInfo);

    // await initAppWithCoreV2();

    return true;
  }

  @override
  Future<bool> onSetAppInfo(
    MethodCall call,
    String currentRoute,
  ) async {
    try {
      if (isSetAppInfo) return true;

      Map<String, dynamic> params = HashMap.from(call.arguments);
      final tokenInfo = TokenInfo.fromJson(params);
      Constants.updateTokenInfo(tokenInfo);
      logDebug("saved token params: ${params.toString()} ");
      env = Constants.environment();
      _updateLocale();

      await TokenManager.saveTokenInfo(
        tokenInfo,
        secureFeature: currentRoute.toSecureFeatureStr(),
      );

      await Constants.updateThemeByFlavor(isFromNative: true);

      isSetAppInfo = true;

      // handle task from queue
      await Future.forEach(
        queues,
        (task) async {
          await task();
        },
      );

      await initAppWithCoreV2();

      await _initSentryData();

      // ignore setAppInfo
      // used when running app from Submodules
      // it is going to use flutter token instead of native token
      // WARNING: debug only
      if (kDebugMode) {
        if (params['useFlutterToken'] is bool) {
          await TokenManager.initToken();
        }
      }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter setAppInfo error',
        data: {'error': e, 'stacktrace': s},
      );

      onAppendError(e.toString());
    }

    // regarding to calendar detail page, don't redirect immediately
    // hang on and wait for passed data from other method call
    _navigateToNextRoute(currentRoute);

    return true;
  }

  @override
  Future<bool> onPop(MethodCall call) async {
    bool needBackToFeed = !Navigator.canPop(Get.context!);
    Get.back(canPop: true);

    return needBackToFeed;
  }

  @override
  Future<bool> onUpdateSwipeBackGestureIfNeeded(MethodCall call) async {
    return Utils.updateNativeSwipeBackGesture();
  }

  @override
  Future<bool> onSetPlatformNavigatorParams(MethodCall call) async {
    PlatformNavigator.routerName = call.arguments['routerName'];
    PlatformNavigator.entryPoint = call.arguments['entryPoint'];
    PlatformNavigator.id = call.arguments['id'];

    // set data if needed
    final arguments = call.arguments['arguments'];
    final setDataMethodName =
        ParserHelper.parseString(call.arguments['setDataMethodName']);
    if (arguments is Map && setDataMethodName?.isNotEmpty == true) {
      switch (setDataMethodName) {
        case PlatformNavigatorSetDataMethod.setCalendarEventObject:
          return _handleSetCalendarEventObjectEvent(arguments);
        case PlatformNavigatorSetDataMethod.setCreateCalendarParams:
          return _handleSetCreateCalendarEventPageParamsEvent(arguments);
        case PlatformNavigatorSetDataMethod.setSelectedColleaguesParams:
          return _handleSetSelectedColleaguesParams(arguments);
        case PlatformNavigatorSetDataMethod.setCreateTaskParams:
          return _handleCreateTask(arguments);
        default:
          break;
      }
    } else {
      final message =
          'PlatformNavigator error, not found\nargument: "$arguments"\nsetDataMethodName: "$setDataMethodName"';
      final error = ErrorDescription(message);
      logDebug('platform navigator error: $error');
      // handleErrorDefault(error);
      // TODO (toannm) handleErrorDefault
    }

    return true;
  }

  void _navigateToNextRoute(String currentRoute) {
    if (currentRoute == CalendarRouterName.calendarEventDetail ||
        currentRoute == CalendarRouterName.createCalendarEvent ||
        currentRoute == CalendarRouterName.calendarCollab ||
        currentRoute == TaskRouterName.tasks ||
        currentRoute == RouterName.selectInvitees ||
        currentRoute == TaskRouterName.taskCollab ||
        currentRoute == TaskRouterName.taskArchiveCollab ||
        currentRoute == TaskRouterName.taskCreate ||
        currentRoute == TaskRouterName.taskProject ||
        currentRoute == RtfEditorRouterName.rtfEditor) {
      // do not need to navigate
    } else {
      if (currentRoute == CoinRoutes.coinMain ||
          currentRoute == PortalRoutes.portalMain) {
        // if (Get.routing.route?.isFirst == true) {
        Get.back();
        // }

        Future.delayed(const Duration(milliseconds: 100)).then((value) {
          Get.toNamed(RouterName.routeWithoutAnimation(currentRoute));
        });
      } else {
        Get.offAndToNamed(RouterName.routeWithoutAnimation(currentRoute));
      }
    }
  }

  Future _initSentryData() async {
    if (kDebugMode) return;
    SentryFlutter.setAppStartEnd(DateTime.now());

    // final String token = await TokenManager.accessToken();
    // final String refreshToken = await TokenManager.refreshToken();

    final transactionName =
        Get.parameters["transaction"] ?? Get.currentRoute.toInteractionStr();

    bool isRootedDevice = await DeviceSafetyInfo.isRootedDevice;
    bool isRealDevice = await DeviceSafetyInfo.isRealDevice;
    bool isDeveloperMode =
        Platform.isAndroid ? await DeviceSafetyInfo.isDeveloperMode : false;

    Sentry.configureScope((scope) {
      scope.transaction = transactionName;

      sentryFingerPrint = '{$transactionName}_$sentryFingerPrint';

      final userData = <String, dynamic>{
        // "token": token,
        // "refreshToken": refreshToken,
      };

      if (Constants.getDeviceId().isNotEmpty) {
        userData['device.id'] = Constants.getDeviceId();
      }

      if (Constants.getDeviceName().isNotEmpty) {
        userData['device.model'] = Constants.getDeviceName();
      }

      userData['gapo_flutter.envi'] = Constants.environment().name;
      userData['gapo_flutter.wsId'] = Constants.workspaceId();
      userData['gapo_flutter.appversion'] = Constants.appVersion;

      String deviceInfo = '';
      if (Platform.isAndroid) {
        deviceInfo = 'isRootedDevice:$isRootedDevice, '
            'isRealDevice:$isRealDevice, '
            'isDeveloperMode:$isDeveloperMode';
      } else if (Platform.isIOS) {
        deviceInfo = 'isRootedDevice:$isRootedDevice, '
            'isRealDevice:$isRealDevice';
      }

      if (deviceInfo.isNotEmpty) {
        userData['device'] = deviceInfo;
      }

      scope.setUser(
        SentryUser(
          id: Constants.userId(),
          name: Constants.displayName(),
          data: userData,
        ),
      );
    });
  }

  Future<void> _updateTokenInfo(
    TokenInfo tokenInfo,
    String currentRoute,
  ) async {
    if (kDebugMode) {
      if (tokenInfo.useFlutterToken ?? false) {
        return;
      }
    }

    Constants.updateTokenInfo(tokenInfo);
    await TokenManager.saveTokenInfo(
      tokenInfo,
      secureFeature: currentRoute.toSecureFeatureStr(),
    );
    env = Constants.environment();

    await Constants.updateThemeByFlavor(isFromNative: true);

    _updateLocale();
  }

  Future<void> _updateUserInfo(TokenInfo tokenInfo) async {
    Constants.updateUserInfo(tokenInfo);

    env = Constants.environment();

    _updateLocale();
  }

  void _updateLocale() {
    if (Get.locale?.languageCode == Constants.language()) {
      return;
    }

    Get.updateLocale(Locale(Constants.language()));
  }

  bool _handleSetCalendarEventObjectEvent(Map arguments) {
    dynamic arg;
    if (arguments['input'] is Map) {
      final json = Map<String, dynamic>.from(arguments['input']);
      final input = CalendarEventDetailInput.fromJson(json);
      arg = input;
    }
    Get.offAndToNamed(
      RouterName.routeWithoutAnimation(CalendarRouterName.calendarEventDetail),
      arguments: arg,
    );
    return true;
  }

  bool _handleSetSelectedColleaguesParams(Map arguments) {
    final json = jsonDecode(arguments["colleagues"]);
    final colleagues =
        List<Assignee>.from(json.map((x) => Assignee.fromJson(x)));
    Get.offAndToNamed(
      RouterName.routeWithoutAnimation(RouterName.selectInvitees),
      arguments: SelectInviteesOptions(
        tabs: [
          SelectInviteeTabs.member,
        ],
        title: LocaleKeys.calendar_calendar_comparison.tr,
        actionButtonTitle: LocaleKeys.calendar_done.tr,
        selectedMembers: colleagues,
      ),
    );
    return true;
  }

  bool _handleCreateTask(Map arguments) {
    Get.offAndToNamed(
      TaskRouterName.taskCreate,
      arguments: TaskCreateArgument(
        section: SectionModel(
          id: arguments["section_id"],
          name: arguments["section_name"],
          taskListId: arguments["task_list_id"],
        ),
        taskListId: arguments["task_list_id"],
        taskListName: arguments["task_list_name"],
        projectId: arguments["project_id"],
        isInCollabGroup: arguments["is_in_collab_group"],
      ),
    );
    return true;
  }

  bool _handleSetCreateCalendarEventPageParamsEvent(Map arguments) {
    var dateTime = DateTime.now();
    var type = CalendarEventType.meeting;
    var duration = 30.minutes;
    var isAllDay = false;
    var googleEmail = '';
    CalendarCollabInput? calendarCollabInput;
    EventRoom? room;
    bool isSynced = false;

    if (arguments['date_time'] is int) {
      dateTime = DateTime.fromMillisecondsSinceEpoch(arguments['date_time']);
    }
    if (arguments['type'] is String) {
      type = calendarEventTypeFromString(arguments['type']) ??
          CalendarEventType.meeting;
    }
    if (arguments['duration'] is int) {
      duration = Duration(seconds: arguments['duration']);
    }
    if (arguments['isAllDay'] is bool) {
      isAllDay = arguments['isAllDay'];
    }
    if (arguments['calendarCollabInput'] is Map) {
      calendarCollabInput = CalendarCollabInput.fromJson(
          Map<String, dynamic>.from(arguments['calendarCollabInput']));
    }
    if (arguments['googleEmail'] is String) {
      googleEmail = arguments['googleEmail'];
    }

    if (arguments['room'] is Map) {
      room = EventRoom.fromJson(Map<String, dynamic>.from(arguments['room']));
    }

    if (arguments['isSynced'] is bool) {
      isSynced = arguments['isSynced'];
    }

    Get.offAndToNamed(
        RouterName.routeWithoutAnimation(
            CalendarRouterName.createCalendarEvent),
        arguments: [
          dateTime,
          type,
          duration,
          isAllDay,
          calendarCollabInput,
          googleEmail,
          room,
          isSynced,
        ]);
    return true;
  }
}
