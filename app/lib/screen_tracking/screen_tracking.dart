import 'package:gp_core/core.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_time_keeping/routes/router_name.dart';

import 'analytics.dart';

class ScreenTracking {
  static final ScreenTracking instance = ScreenTracking._();

  final Map<String, ScreenTrackingName> _coreTrackScreens = {
    RouterName.selectInvitees: ScreenTrackingName.selectInviteesScreen,
    "/MediaPreviewList": ScreenTrackingName.mediaPreviewList,
  };

  // final Map<String, ScreenTrackingName> _albumTrackScreens = {
  //   AlbumRouterName.album: ScreenTrackingName.album,
  //   AlbumRouterName.albumDetail: ScreenTrackingName.albumDetailScreen,
  //   AlbumRouterName.createAlbum: ScreenTrackingName.albumCreate,
  // };

  final Map<String, ScreenTrackingName> _taskTrackScreens = {
    TaskRouterName.taskGeneral: ScreenTrackingName.taskGeneral,
    TaskRouterName.taskProject: ScreenTrackingName.taskProject,
    TaskRouterName.taskProjectPicker: ScreenTrackingName.taskProjectPicker,
    TaskRouterName.taskArchive: ScreenTrackingName.taskArchive,
    TaskRouterName.taskPickAssigneeFilter:
        ScreenTrackingName.taskPickAssigneeFilter,
    TaskRouterName.taskClone: ScreenTrackingName.taskClone,
    TaskRouterName.taskCollab: ScreenTrackingName.taskCollab,
    TaskRouterName.tasks: ScreenTrackingName.taskListScreen,
    TaskRouterName.taskCreate: ScreenTrackingName.createTaskScreen,
    TaskRouterName.taskDescription: ScreenTrackingName.taskDescriptionScreen,
    TaskRouterName.upload: ScreenTrackingName.taskUploadScreen,
    TaskRouterName.notifications: ScreenTrackingName.taskNotificationScreen,
    TaskRouterName.assigneePicker: ScreenTrackingName.addPeopleScreen,
    TaskRouterName.taskSearch: ScreenTrackingName.taskSearchScreen,
    TaskRouterName.projectCreateScreen: ScreenTrackingName.projectCreateScreen,
    TaskRouterName.taskArchiveCollab: ScreenTrackingName.taskArchiveCollab,
    TaskRouterName.customRepeatScreen: ScreenTrackingName.taskCustomRepeat,
    TaskRouterName.taskMain: ScreenTrackingName.taskMain,
  };

  final Map<String, ScreenTrackingName> _calendarTrackScreens = {
    CalendarRouterName.calendar: ScreenTrackingName.calendar,
    CalendarRouterName.calendarNotifications:
        ScreenTrackingName.calendarNotificationsScreen,
    CalendarRouterName.calendarEventDetail:
        ScreenTrackingName.calendarEventDetailScreen,
    CalendarRouterName.createCalendarEvent:
        ScreenTrackingName.createCalendarEventScreen,
    CalendarRouterName.calendarDescription:
        ScreenTrackingName.calendarDescriptionScreen,
    CalendarRouterName.calendarRoomSelect:
        ScreenTrackingName.calendarRoomSelectScreen,
    CalendarRouterName.calendarCollab: ScreenTrackingName.calendarCollab,
    CalendarRouterName.calendarRepeatCustomize:
        ScreenTrackingName.calendarRepeatCustomize,
    CalendarRouterName.listInvitees: ScreenTrackingName.listInvitees,
    CalendarRouterName.listEmailInvitees: ScreenTrackingName.listEmailInvitees,
    CalendarRouterName.changeRoomBooking: ScreenTrackingName.changeRoomBooking,
  };

  final Map<String, ScreenTrackingName> _timeKeepingTrackScreens = {
    TimeKeepingRoutes.timeKeeping: ScreenTrackingName.timeKeeping,
  };

  final _trackScreens = <String, ScreenTrackingName>{};

  ScreenTracking._() {
    _trackScreens.addAll(_coreTrackScreens);
    _trackScreens.addAll(_taskTrackScreens);
    // _trackScreens.addAll(_albumTrackScreens);
    _trackScreens.addAll(_calendarTrackScreens);
    _trackScreens.addAll(_timeKeepingTrackScreens);
  }

  void trackScreen({String? screenName, String? routeName}) {
    if (routeName != null && (screenName == null || screenName.isEmpty)) {
      screenName = _mapRouteNameToScreenTrackingName(routeName);
    }
    if (screenName != null) {
      Deeplink.sendScreenTrackingEvent(screenName);
    } else {
      logDebug('Unable to track screen due to invalid parameters');
    }
  }

  String? _mapRouteNameToScreenTrackingName(String routeName) {
    final value =
        _trackScreens[routeName.replaceAll("Root", "")]; //TRACK SCREEN
    if (value != null) {
      return value.stringValue;
    }
    return null;
  }
}
