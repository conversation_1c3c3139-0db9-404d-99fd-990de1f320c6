enum ScreenTrackingName {
  taskListScreen,
  createTaskScreen,
  taskDetailsScreen,
  taskDescriptionScreen,
  addPeopleScreen,
  taskNotificationScreen,
  taskUploadScreen,
  album,
  albumCreate,
  calendar,
  calendarNotificationsScreen,
  calendarEventDetailScreen,
  taskSearchScreen,
  createCalendarEventScreen,
  selectInviteesScreen,
  calendarDescriptionScreen,
  calendarRoomSelectScreen,
  albumDetailScreen,
  mediaPreviewList,
  taskGeneral,
  taskProject,
  taskProjectPicker,
  taskArchive,
  taskPickAssigneeFilter,
  taskClone,
  taskCollab,
  calendarCollab,
  calendarRepeatCustomize,
  projectCreateScreen,
  taskArchiveCollab,
  listInvitees,
  listEmailInvitees,
  changeRoomBooking,
  taskCustomRepeat,
  timeKeeping,
  taskMain,
}

extension Name on ScreenTrackingName {
  String get stringValue {
    switch (this) {
      case ScreenTrackingName.taskListScreen:
        return 'taskListScreen';
      case ScreenTrackingName.createTaskScreen:
        return 'createTaskScreen';
      case ScreenTrackingName.taskDetailsScreen:
        return 'taskDetailsScreen';
      case ScreenTrackingName.taskDescriptionScreen:
        return 'taskDescriptionScreen';
      case ScreenTrackingName.addPeopleScreen:
        return 'addPeopleScreen';
      case ScreenTrackingName.taskNotificationScreen:
        return 'taskNotificationScreen';
      case ScreenTrackingName.taskUploadScreen:
        return 'taskUploadScreen';
      case ScreenTrackingName.album:
        return 'album';
      case ScreenTrackingName.albumCreate:
        return 'albumCreate';
      case ScreenTrackingName.calendar:
        return 'calendar';
      case ScreenTrackingName.calendarNotificationsScreen:
        return 'calendarNotificationScreen';
      case ScreenTrackingName.calendarEventDetailScreen:
        return 'calendarEventDetailScreen';
      case ScreenTrackingName.taskSearchScreen:
        return 'taskSearchScreen';
      case ScreenTrackingName.createCalendarEventScreen:
        return 'createCalendarEventScreen';
      case ScreenTrackingName.selectInviteesScreen:
        return 'selectInviteesScreen';
      case ScreenTrackingName.albumDetailScreen:
        return 'albumDetailScreen';
      case ScreenTrackingName.calendarDescriptionScreen:
        return 'calendarDescriptionScreen';
      case ScreenTrackingName.mediaPreviewList:
        return 'mediaPreviewList';
      case ScreenTrackingName.taskGeneral:
        return 'taskGeneral';
      case ScreenTrackingName.taskProject:
        return 'taskProject';
      case ScreenTrackingName.taskProjectPicker:
        return 'taskProjectPicker';
      case ScreenTrackingName.taskArchive:
        return 'taskArchive';
      case ScreenTrackingName.taskPickAssigneeFilter:
        return 'taskPickAssigneeFilter';
      case ScreenTrackingName.taskClone:
        return 'taskClone';
      case ScreenTrackingName.taskCollab:
        return 'collabTask';
      case ScreenTrackingName.projectCreateScreen:
        return 'taskProjectCreateScreen';
      case ScreenTrackingName.taskArchiveCollab:
        return 'collabTaskArchive';
      case ScreenTrackingName.calendarCollab:
        return 'collabCalendar';
      case ScreenTrackingName.calendarRepeatCustomize:
        return 'calendarRepeatCustomize';
      case ScreenTrackingName.listInvitees:
        return 'calendarParticipantList';
      case ScreenTrackingName.calendarRoomSelectScreen:
        return 'calendarRoomSelectScreen';
      case ScreenTrackingName.listEmailInvitees:
        return 'calendarEmailInvitessList';
      case ScreenTrackingName.changeRoomBooking:
        return 'calendarChangeRoomBooking';
      case ScreenTrackingName.taskCustomRepeat:
        return 'taskCustomRepeat';
      case ScreenTrackingName.timeKeeping:
        return "timeKeeping";
      case ScreenTrackingName.taskMain:
        return "taskMain";
    }
  }
}
