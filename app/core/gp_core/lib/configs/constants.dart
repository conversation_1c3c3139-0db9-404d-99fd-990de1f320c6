import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/configs/apple_device.dart';
import 'package:gp_core/core.dart';

enum Environment { staging, uat, production, momoOnPremise }

extension EnvironmentString on String {
  Environment get env {
    Environment result;
    switch (this) {
      case "staging":
        result = Environment.staging;
        break;
      case "uat":
        result = Environment.uat;
        break;
      case "production":
        result = Environment.production;
        break;
      case "momo":
        result = Environment.momoOnPremise;
        break;
      default:
        throw UnimplementedError();
    }
    return result;
  }

  Environment get envByUrl {
    Environment result;
    switch (this) {
      case "https://staging-api.gapowork.vn":
        result = Environment.staging;
        break;
      case "https://uat-api.gapowork.vn":
        result = Environment.uat;
        break;
      case "https://api.gapowork.vn":
        result = Environment.production;
        break;
      case "https://momo-api.gapowork.app":
        result = Environment.momoOnPremise;
        break;
      default:
        throw UnimplementedError();
    }
    return result;
  }
}

extension EnvironmentUrl on Environment {
  String get url {
    String result = "";
    switch (this) {
      case Environment.staging:
        result = "https://staging-api.gapowork.vn";
        break;
      case Environment.uat:
        result = "https://uat-api.gapowork.vn";
        break;
      case Environment.production:
        result = "https://api.gapowork.vn";
        break;
      case Environment.momoOnPremise:
        result = "https://momo-api.gapowork.app";
        break;
    }
    return result;
  }

  String get webBaseUrl {
    String result = "";
    switch (this) {
      case Environment.staging:
        result = "https://staging.gapowork.vn";
        break;
      case Environment.uat:
        result = "https://uat.gapowork.vn";
        break;
      case Environment.production:
        result = "https://app.gapowork.vn";
        break;
      case Environment.momoOnPremise:
        result = "https://momo.gapowork.vn";
        break;
    }
    return result;
  }

  String get mqttTCPUrl {
    String result = "";
    switch (this) {
      case Environment.staging:
        result = "staging-mqtt.gapowork.vn";
        break;
      case Environment.uat:
        result = "uat-mqtt.gapowork.vn";
        break;
      case Environment.production:
        result = "mqtt.gapowork.vn";
        break;
      case Environment.momoOnPremise:
        result = "momo-mqtt.gapowork.app";
        break;
    }
    return result;
  }

  String get chatDomain {
    String result = "";
    switch (this) {
      case Environment.staging:
        result = "https://staging-messenger.gapowork.vn/chat/v3.3";
        break;
      case Environment.uat:
        result = "https://uat-messenger.gapowork.vn/chat/v3.3";
        break;
      case Environment.production:
        result = "https://messenger.gapowork.vn/chat/v3.3";
        break;
      case Environment.momoOnPremise:
        result = "https://momo-messenger.gapowork.app/";
        break;
    }
    return result;
  }

  String get uploadDomain {
    String result = "";
    final bool isEnableSecure = FeatureFlag.enableSecureUpload;

    final String uploadUrl = isEnableSecure ? _secureUploadUrl : _uploadUrl;

    switch (this) {
      case Environment.staging:
        result = "https://staging-upload.gapowork.vn/$uploadUrl";
        break;
      case Environment.uat:
        result = "https://uat-upload.gapowork.vn/$uploadUrl";
        break;
      case Environment.production:
        result = "https://upload.gapowork.vn/$uploadUrl";
        break;
      case Environment.momoOnPremise:
        result = "https://momo-upload.gapowork.app";
        break;
    }
    return result;
  }
}

const _secureUploadUrl = 'secure/v1.0';
const _uploadUrl = 'media/v1.0';

Environment env = Environment.staging;

class Constants {
  static const String appVersion = "5.7.2";

  static TokenInfo _tokenInfo = TokenInfo(
    userId: '635112032',
    workspaceId: '581860791816317',
    displayName: 'Do Khanh Toan',
  );

  static TokenInfo updateAccessToken(String accessToken) {
    return _tokenInfo.copyWithAccessToken(accessToken);
  }

  static String get baseUrl {
    String url = _tokenInfo.apiBaseUrl ?? env.url;
    if (url.endsWith("/")) {
      url = url.substring(0, url.length - 1);
    }
    return url;
  }

  static String get appDomain {
    String url = _tokenInfo.webBaseUrl ?? env.webBaseUrl;
    if (url.endsWith("/")) {
      url = url.substring(0, url.length - 1);
    }
    return url;
  }

  static String get mqttHost {
    String url = _tokenInfo.mqttTCPUrl ?? env.mqttTCPUrl;

    if (url.endsWith("/")) {
      url = url.substring(0, url.length - 1);
    }
    return url;
  }

  static String get chatDomain {
    String url = _tokenInfo.messengerBaseUrl ?? env.chatDomain;
    if (url.endsWith("/")) {
      url = url.substring(0, url.length - 1);
    }
    if (!url.endsWith("/chat/v3.3")) {
      url = "$url/chat/v3.3";
    }
    return url;
  }

  /// to upload files, media, etc.
  static String get uploadDomain {
    String url = _tokenInfo.uploadBaseUrl ?? env.uploadDomain;

    if (url.endsWith("/")) {
      url = url.substring(0, url.length - 1);
    }

    if (!url.endsWith("/$_uploadUrl") && !url.endsWith("/$_secureUploadUrl")) {
      if (FeatureFlag.enableSecureUpload) {
        url = "$url/$_secureUploadUrl";
      } else {
        url = "$url/$_uploadUrl";
      }
    }

    return url;
  }

  static String apiDomain = baseUrl;

  static String get calendarDomain => '$baseUrl/calendar/v1.0';

  static String get meetingCollabDomain =>
      '$baseUrl/collab-meeting-api/v1.0/api';

  static String taskDomain = '$baseUrl/mini-task/v1.0';

  static String searchDomain = '$baseUrl/search/v2.0';
  static String searchGoDomain = '$baseUrl/search-go/v2.0';

  static String commentDomain = '$baseUrl/comment/v2.1';

  static String workspaceDomain = '$baseUrl/workspace/v1.0';

  static String albumDomain = '$baseUrl/album/v1.0';

  static String postDomain = '$baseUrl/main/v1.4/post';

  static String userInfoDomain = '$baseUrl/user-info/v1.0';

  static String collabDomain = '$baseUrl/collab-membership/v1.0/collab-group';

  static String botDomain = '$baseUrl/3rd-bot/v1.0';

  static String timeKeepingDomain = '$baseUrl/timekeeping/v2.0';

  static String approvalDomain = '$baseUrl/approval/v4.1/api';

  static String get organizationDomain {
    return '$baseUrl/organization-chart/v2.0/workspaces/${Constants.workspaceId()}';
  }

  static String get organizationDomainV3 {
    return '$baseUrl/organization-chart/v3.0';
  }

  static String get commentDomainV2 {
    return '$baseUrl/comment/v2.1';
  }

  static String googleDriveGetFile(String fileId) {
    return "$_googleDriveApi/$fileId?alt=media";
  }

  static const String fileThumbDomain = 'files-1.gapowork.vn';
  static const String authAPI = '/auth/v3.1';
  static const String loginPath = '/login';
  static const String checkEmailPath = '/check-email';
  static const String checkPhoneNumberPath = '/check-phone-number';
  static const String userMePath = '/users/me';
  static const String renewTokenPath = '/renew-token';
  static const String calendarEvents = '/events';
  static const String dragCalendarEvent = '/events/%/drag';
  static const String undoDraggedCalendarEvent = '/events/%/undo';
  static const String signInGG = '/gg-calendar';
  static const String signOutGG = '/gg-calendar/stop';
  static const String trackSyncGG = '/gg-calendar/has-synced';
  static const String eventTrackSync = '/gg-calendar/event/checking/sync';
  static const String ggScopeChecking = "/gg-calendar/scopes/checking";
  static const String ggToken = '/gg-calendar/token';
  static const String syncAuth = '/sync/auth';
  static const String configs = '/configs';
  static const String workspaces = '/workspaces';

  static const String collabMeeting = '/collab-meeting';

  static const String _googleDriveApi =
      'https://www.googleapis.com/drive/v3/files';

  static String _deviceId = '';
  static String _deviceName = '';
  static int _androidSdkInt = 21;
  static String? currentWorkspaceId;

  static void updateDeviceId(String deviceId) {
    _deviceId = deviceId;
  }

  static String getDeviceId() {
    return _deviceId;
  }

  static void updateDeviceName(String deviceName) {
    _deviceName = deviceName;
  }

  static void updateAndroidSdkInt(int androidSdkInt) {
    _androidSdkInt = androidSdkInt;
  }

  static String getDeviceName() {
    return _deviceName;
  }

  static int getAndroidSdkInt() {
    return _androidSdkInt;
  }

  static List<String>? _workspaceIds;
  static void setWorkspaceIds(List<String> ids) {
    _workspaceIds = ids;
  }

  static List<String>? get workspaceIds {
    return _workspaceIds;
  }

  static void updateTokenInfo(TokenInfo tokenInfo) {
    _tokenInfo = tokenInfo;

    _userAgent().then((userAgent) => _tokenInfo.userAgent = userAgent);
  }

  static void updateCurrentWorkspaceId(String workspaceId) {
    if (workspaceId.isEmpty) return;

    currentWorkspaceId = workspaceId;
  }

  static String workspaceId() {
    return currentWorkspaceId ?? _tokenInfo.workspaceId ?? '';
  }

  static String workspaceThumbnail() {
    return _tokenInfo.workspaceThumbnail ?? '';
  }

  static String userId() {
    return _tokenInfo.userId ?? '';
  }

  static String displayName() {
    return _tokenInfo.displayName ?? '';
  }

  static String language() {
    return (_tokenInfo.language ?? 'vi').toLowerCase();
  }

  static String? avatar() {
    return _tokenInfo.avatar;
  }

  static bool canDownload() {
    return _tokenInfo.featureInfo?.isEnableDownloadFile ?? true;
  }

  static Environment environment() {
    if (_tokenInfo.apiBaseUrl?.isNotEmpty == true) {
      return _tokenInfo.apiBaseUrl?.envByUrl ?? Environment.staging;
    } else {
      return _tokenInfo.environment?.env ?? Environment.staging;
    }
  }

  static String? androidMSClientId() {
    return _tokenInfo.androidMSClientId;
  }

  static String? androidMSRedirectUrl() {
    return _tokenInfo.androidMSRedirectUrl;
  }

  static String? userAgent() {
    return _tokenInfo.userAgent;
  }

  static bool isAdminCurrentWorkspace() {
    return _tokenInfo.isCurrentAdminWorkspace;
  }

  static Future<String> _userAgent() async {
    String? uafti = _tokenInfo.userAgent;
    // GapoWork/2.11.2 (Flutter-android sdk_gphone64_arm64;REL;})
    // GapoWork/2.11.2 (Flutter-ios15.0; iPhone)
    if (uafti == null || uafti == "") {
      return await _flutterUserAgent();
    }

    return uafti;
  }

  static Future<String> _flutterUserAgent() async {
    String deviceInfo = await _deviceInfo();
    return "GapoWork/$appVersion (Flutter$deviceInfo)"
        .replaceAll(specialCharRegex, "");
  }

  static Future<String> _deviceInfo() async {
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

    try {
      if (GetPlatform.isAndroid) {
        var info = await deviceInfoPlugin.androidInfo;
        Constants.updateDeviceName(info.model);
        Constants.updateAndroidSdkInt(info.version.sdkInt);
        return "-android ${info.version.release}; ${info.model}; ${info.brand} ${info.device}}";
      } else if (GetPlatform.isIOS) {
        var info = await deviceInfoPlugin.iosInfo;
        final utsname = info.utsname.machine;
        final deviceName = AppleDevices.mapToDevice(utsname);
        Constants.updateDeviceName(deviceName);
        return "-ios ${info.systemVersion}; ${info.model}";
      }
    } on PlatformException {
      logDebug('Failed to get platform version');
    }

    Constants.updateDeviceName("Mobile");

    return "";
  }

  // static String mapToIosDevice(String utsname)

  static bool get isStaging => environment() == Environment.staging;

  static bool get isUAT => environment() == Environment.uat;

  static bool get isProduction => environment() == Environment.production;

  static bool get isOnPremise => environment() == Environment.momoOnPremise;

  static const weekdayIntToStr = {
    1: 'MO',
    2: 'TU',
    3: 'WE',
    4: 'TH',
    5: 'FR',
    6: 'SA',
    7: 'SU'
  };

  static const weekdayStrToInt = {
    'MO': 1,
    'TU': 2,
    'WE': 3,
    'TH': 4,
    'FR': 5,
    'SA': 6,
    'SU': 7
  };

  static bool useSvgImage = true;

  // chỉ update userinfo trong trong tokeninfo
  static void updateUserInfo(TokenInfo tokenInfo) {
    if (tokenInfo.avatar?.isNotEmpty == true) {
      _tokenInfo.avatar = tokenInfo.avatar;
    }
    if (tokenInfo.workspaceId?.isNotEmpty == true) {
      _tokenInfo.workspaceId = tokenInfo.workspaceId;
    }
    if (tokenInfo.displayName?.isNotEmpty == true) {
      _tokenInfo.displayName = tokenInfo.displayName;
    }
    if (tokenInfo.language?.isNotEmpty == true) {
      _tokenInfo.language = tokenInfo.language;
    }
  }

  static void updateWorkspace(String workspaceId) {
    _tokenInfo.workspaceId = workspaceId;
  }

  static bool get needToShowLog {
    return (GetPlatform.isIOS || GetPlatform.isAndroid) &&
        (kDebugMode || kProfileMode);
  }

  static Future updateThemeByFlavor({bool isFromNative = false}) async {
    GPColorByFlavor gpColorByFlavor = !isFromNative
        ? env == Environment.momoOnPremise
            ? GPColorByFlavor.onPremiseMomo
            : GPColorByFlavor.saas
        : Constants.isOnPremise
            ? GPColorByFlavor.onPremiseMomo
            : GPColorByFlavor.saas;

    return await GPColor.saveFlavor(gpColorByFlavor);
  }
}

extension WeekdayConvertible on Constants {}

class DebugConfig {
  static const bool curlOnRequest = false;
  static const bool curlOnResponse = true;
  static const bool curlOnError = true;
}

/// ios arguments:
/*
{
  "brand_logo": "https://image-1.gapo.vn/icon/momo_logo.png",
  "api_base_url": "https://momo-api.gapowork.app",
  "refreshToken": "1993045.459c08da-ff57-4532-9da4-f896f4751232.ggffnpcjp9xypy1i93w3.6n6rwo86qmx7u8aahgrq",
  "messenger_base_url": "https://momo-messenger.gapowork.app",
  "language": "vi",
  "brand_name": "momo life",
  "noti_push_base_url": "https://momo-api.gapowork.app",
  "userId": "1993045",
  "environment": "production",
  "upload_base_url": "https://momo-upload.gapowork.app",
  "mqtt_tcp_url": "tcp://momo-mqtt.gapowork.app",
  "displayName": "Tester",
  "workspaceId": "582876549419264",
  "accessToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImp0aSI6IjE5OTMwNDUuNDU5YzA4ZGEtZmY1Ny00NTMyLTlkYTQtZjg5NmY0NzUxMjMyLjE2OTgxMTgxMzQifQ.eyJpc…",
  "workspace_thumbnail": "https://gapowork-images.s3.momoapp.vn/images/2666eb92-7e8f-4a24-b9a4-c9f6bf7d9802/blob.jpeg",
  "avatar": "https://momo-cdn-thumb-i-2.gapowork.app/$size$/smart/8aca8719-e19f-4e12-8e4e-dfc36e6afe0e/blob.jpeg",
  "web_base_url": "https://momolife.vn"
}
*/

/// android arguments
/*
{
  "userId": "1993045",
  "displayName": "Tester",
  "avatar": "https://momo-cdn-thumb-i-2.gapowork.app/$size$/smart/8aca8719-e19f-4e12-8e4e-dfc36e6afe0e/blob.jpeg",
  "accessToken": " *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************, refreshToken=1993045.fd60162f-db78-48f7-be81-1b3a8f2b56a8.l7fs6x8fuoh5g1mhedwu.qixdyecuaxji05u9kago",
  "workspaceId": "582891695201557",
  "language": "vi",
  "web_base_url": "https://momolife.vn/",
  "api_base_url": "https://momo-api.gapowork.app/",
  "upload_base_url": "https://momo-upload.gapowork.app/",
  "messenger_base_url": "https://momo-messenger.gapowork.app/",
  "mqtt_tcp_url": "tcp://momo-mqtt.gapowork.app",
  "environment": "production",
  "msal": {
    "client_id": "a68c15f7-e681-4d91-b0c8-5170ed465fa1",
    "redirect_url": "msauth://work.vn.gapo.app/6HZISiyBMNQWKALoeTFCZCZZFos%3D"
  },
  "brand_name": "momo life",
  "brand_logo": "https://image-1.gapo.vn/icon/momo_logo.png"
}
*/
