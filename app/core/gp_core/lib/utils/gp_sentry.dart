// ignore_for_file: constant_identifier_names

import 'package:gp_core/core.dart';

const _KEY_MESSAGE = 'MESSAGE';
const _KEY_ERROR = 'ERROR';
const _KEY_PERMISSIONS = 'PERMISSIONS';
const _KEY_CHECKIN = 'CHECKIN';
const _KEY_CHECKOUT = 'CHECKIN';
const _KEY_OTHER = 'OTHER';

enum GPTrackerType {
  core,
  album,
  calendar,
  drive,
  task,
  memberPicker,
  timeKeeping,
  ca,
  nativeShared,
  collabTask,
  collabCalendar,
  ticket,
}

extension GPTrackerTypeExt on GPTrackerType {
  String get loggerName {
    switch (this) {
      case GPTrackerType.core:
        return 'Flutter:core';
      case GPTrackerType.album:
        return 'Flutter:album';
      case GPTrackerType.calendar:
        return 'Flutter:calendar';
      case GPTrackerType.drive:
        return 'Flutter:drive';
      case GPTrackerType.task:
        return 'Flutter:task';
      case GPTrackerType.memberPicker:
        return 'Flutter:memberPicker';
      case GPTrackerType.timeKeeping:
        return 'Flutter:timeKeeping';
      case GPTrackerType.ca:
        return 'Flutter:ca';
      case GPTrackerType.nativeShared:
        return 'Flutter:nativeShared';
      case GPTrackerType.collabTask:
        return 'Flutter:collabTask';
      case GPTrackerType.collabCalendar:
        return 'Flutter:collabCalendar';
      case GPTrackerType.ticket:
        return 'Flutter:ticket';
    }
  }
}

class GPCoreTracker {
  GPCoreTracker._();

  static final _instance = GPCoreTracker._();

  factory GPCoreTracker() => _instance;

  final List<Breadcrumb> _sentryBreadcrumbs = [];

  void _sentryLogInfo(
    String message, {
    String? category,
    Map<String, dynamic>? data,
  }) {
    final breadcrumb = Breadcrumb(
      message: message,
      timestamp: DateTime.now(),
      category: category,
      level: SentryLevel.info,
      data: data,
    );
    _sentryBreadcrumbs.add(breadcrumb);

    Sentry.addBreadcrumb(breadcrumb);
  }

  void appendMessage(
    String message, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      message,
      data: data,
      category: _KEY_MESSAGE,
    );
  }

  void appendError(
    String message, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      message,
      data: data,
      category: _KEY_ERROR,
    );
  }

  void appendPermission(
    String permission, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      permission,
      data: data,
      category: _KEY_PERMISSIONS,
    );
  }

  void appendCheckin(
    String checkin, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      checkin,
      data: data,
      category: _KEY_CHECKIN,
    );
  }

  void appendCheckout(
    String checkout, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      checkout,
      data: data,
      category: _KEY_CHECKOUT,
    );
  }

  void appendOther(
    String message, {
    Map<String, dynamic>? data,
  }) {
    _sentryLogInfo(
      message,
      data: data,
      category: _KEY_OTHER,
    );
  }

  void checkAssignees(ListAPIResponse<Assignee> response) {
    try {
      if (response.data?.isEmpty == true) return;

      final users = <String, String>{};
      for (var element in response.data ?? <Assignee>[]) {
        if (element.displayName.isEmpty) {
          users.addAll({
            'id': element.id.toString(),
            'mail': element.email ?? '',
            'fullName': element.fullName ?? '',
          });
        }
      }

      if (users.isEmpty) {
        appendError(
          'Flutter:core.checkAssignees',
          data: {'users': users},
        );

        sendLog(message: 'Flutter:core.checkAssignees');
      }
    } catch (e, s) {
      appendError(
        'Flutter:core.checkAssignees.error',
        data: {'error': e, 'stacktrace': s},
      );

      sendLog(message: 'Flutter:core.checkAssignees.error');
    }
  }

  void clear() {
    _sentryBreadcrumbs.clear();
  }

  void sendLog({
    String message = 'GPCoreTracker',
    GPTrackerType trackerType = GPTrackerType.core,
  }) {
    if (_sentryBreadcrumbs.isEmpty) {
      return;
    }

    Sentry.captureEvent(
      SentryEvent(
        message: SentryMessage(message),
        logger: trackerType.loggerName,
        level: SentryLevel.info,
        breadcrumbs: _sentryBreadcrumbs,
        fingerprint: [message],
      ),
    );

    clear();
  }
}
