import 'package:gp_core/core.dart';

part 'upload_image_response_model.g.dart';

@JsonSerializable()
class UploadImageResponseModel {
  UploadImageResponseModel({
    this.id,
    this.userId,
    this.src,
    this.url,
    this.type,
    this.source,
    this.size,
    this.fileType,
    this.category,
    this.width,
    this.height,
    this.quality,
    this.fileName,
    this.fileLink,
    this.uploadType,
  });

  @Json<PERSON>ey(name: 'id')
  String? id;

  @Json<PERSON>ey(name: 'user_id')
  String? userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'src')
  String? src;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'url')
  UploadFileURLResponseModel? url;

  @J<PERSON><PERSON><PERSON>(name: 'type')
  String? type;

  @Json<PERSON>ey(name: 'source')
  String? source;

  @Json<PERSON>ey(name: 'size')
  int? size;

  @J<PERSON><PERSON>ey(name: 'file_type')
  String? fileType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'category')
  String? category;

  @Json<PERSON>ey(name: 'width')
  int? width;

  @J<PERSON><PERSON><PERSON>(name: 'height')
  int? height;

  @J<PERSON><PERSON><PERSON>(name: 'quality')
  String? quality;

  @Json<PERSON><PERSON>(readValue: readValue)
  String? fileName;

  @JsonKey(name: 'file_link')
  String? fileLink;

  String? uploadType;

  // String? get thumbPattern {
  //   if (id != null && fileType != null) {
  //     final String fileName = '$id.$fileType';
  //     return 'https://cdn-thumb-image-1.gapowork.vn/\$size\$/smart/$fileName';
  //   }

  //   return null;
  // }

  factory UploadImageResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UploadImageResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$UploadImageResponseModelToJson(this)
    ..addAll({
      'name': fileName,

      /// 30/07/2025: BE và web đang sử dụng "name". ToanNM check thấy flow khi upload video cũng là name, ăn theo file, nên gửi thêm "name" cho đồng nhất (không bỏ fileName)
    });

  static Object? readValue(Map<dynamic, dynamic> json, String key) {
    return json[key] ?? json['file_name'] ?? json['name'];
  }
}
/**
  {
    "id": "6dd8024e-aa8d-4a9f-a086-aaf3f85c368f",
    "user_id": "635112032",
    "src": "https://gapo-work-image.statics.pancake.vn/images/6dd8024e-aa8d-4a9f-a086-aaf3f85c368f.jpeg",
    "url": {
      "store": "gapo-work-image.statics.pancake.vn",
      "src": "/images/6dd8024e-aa8d-4a9f-a086-aaf3f85c368f.jpeg"
    },
    "type": "image",
    "source": "source",
    "size": 5683615,
    "file_type": "jpeg",
    "category": "default",
    "width": 4288,
    "height": 2848,
    "quality": "hd"
  }
 */
